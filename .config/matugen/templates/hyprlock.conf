# # BACKGROUND
# background {
#     monitor =
#     path = {{ image }}
#     blur_passes = 3
#     contrast = 0.8916
#     brightness = 0.8172
#     vibrancy = 0.1696
#     vibrancy_darkness = 0.0
# }

# # GENERAL
# general {
#     no_fade_in = false
#     grace = 0
#     disable_loading_bar = false
# }

# # Time-Hour
# label {
#     monitor =
#     text = cmd[update:1000] echo "<span>$(date +"%I")</span>"
#     color = {{ colors.primary.default.rgb }}
#     font_size = 125
#     font_family = StretchPro
#     position = -80, 190
#     halign = center
#     valign = center
# }

# # Time-Minute
# label {
#     monitor =
#     text = cmd[update:1000] echo "<span>$(date +"%M")</span>"
#     color = {{ colors.secondary.default.rgb }}
#     font_size = 125
#     font_family = StretchPro
#     position = 0, 70
#     halign = center
#     valign = center
# }

# # Day-Month-Date
# label {
#     monitor =
#     text = cmd[update:1000] echo -e "$(date +"%d %B, %a.")"
#     color = {{ colors.primary.default.rgb }}
#     font_size = 19
#     font_family = "Rubik Medium"
#     position = 20, -10
#     halign = center
#     valign = center
# }

# # USER
# label {
#     monitor =
#     text =     $USER
#     color = {{ colors.primary.default.rgb }}
#     outline_thickness = 2
#     dots_size = 0.15 # Scale of input-field height, 0.2 - 0.8
#     dots_spacing = 0.15 # Scale of dots' absolute size, 0.0 - 1.0
#     dots_center = true
#     font_size = 12
#     font_family = Rubik
#     position = 0, -300
#     halign = center
#     valign = center
# }

# # INPUT FIELD
# input-field {
#     monitor =
#     size = 210, 40
#     outline_thickness = 2
#     dots_size = 0.2 # Scale of input-field height, 0.2 - 0.8
#     dots_spacing = 0.2 # Scale of dots' absolute size, 0.0 - 1.0
#     dots_center = true
#     outer_color = {{ colors.outline.default.rgb }}
#     inner_color = {{ colors.surface.default.rgb }}
#     font_color = {{ colors.primary.default.rgb }}
#     fade_on_empty = false
#     font_family = SF Pro Display
#     placeholder_text = <i>Enter Password</i>
#     hide_input = false
#     position = 0, -340
#     halign = center
#     valign = center
# }

# # # CURRENT SONG
# label {
#     monitor =
#     text =cmd[update:10] echo "$(~/.config/hypr/Scripts/songdetail.sh)"              
#     color = {{ colors.secondary.default.rgb }}
#     font_size = 14
#     font_family = JetBrains Mono Nerd, SF Pro Display Bold
#     position = 0, 20
#     halign = center
#     valign = bottom
# }

$PMF = Speculum
$SMF = Departure Mono
$SMFS = 14
$FCOL1 = rgba(235, 215, 225, 1.0)
$WTEMP = metar get | sed 's/, /,/' | awk -v FS="Temperature: " 'NF>1{print $2}' | rev | cut -f1 -d"(" | rev | cut -f1 -d"C" | xargs
$CTEMP = echo "$(cat /sys/class/thermal/thermal_zone2/temp)/1000" | bc 
$LOAD = top -bn1 | grep "Cpu(s)" | \sed "s/.*, *\([0-9.]*\)%* id.*/\1/" | awk '{print 100 - $1"%"}'
$WTHR = metar get | head -1 | cut -d'(' -f1

# BACKGROUND
background {
    monitor =
     path = {{ image }}
    blur_passes = 3
    blur_size = 3
    noise = 0.05
    contrast = 1
    brightness = 0.8
    vibrancy = 0.2
    vibrancy_darkness = 0.2
}

# GENERAL
general {
    no_fade_in = true
    no_fade_out = true
    hide_cursor = true
    grace = 0
    disable_loading_bar = true
}

# INPUT FIELD
input-field {
    monitor = 
    size = 300, 50label 
    outline_thickness = 2
    dots_size = 0.2 # Scale of input-field height, 0.2 - 0.8
    dots_spacing = 0.5 # Scale of dots' absolute size, 0.0 - 1.0
    dots_center = true
    dots_text_format = <b>*</b>
    outer_color = rgba(0, 0, 0, 0)
    inner_color = rgba(225, 215, 244, 0.2)
    font_color = rgba(205, 214, 244, 1)
    font_family = JetBrainsMono Nerd Font Mono
    font_size = 30
    fade_on_empty = false
    rounding = 0
    check_color = rgb(204, 136, 34)
    placeholder_text = <b><span foreground="##cdd6f4">...</span></b>
    fail_text = <b><span>!!!</span></b> 
    hide_input = false 
    position = 0, -120
    halign = center
    valign = center
}

# DATE
label {
  monitor = 
  text = cmd[update:1000] echo "<span fgalpha='75%'>$(date +"%D")</span>"
  color = $FCOL1
  font_size = 60
  font_family = $PMF
  shadow_passes = 2
  shadow_size = 2
  shadow_color = rgb(160,160,160)
  position = 355, -35
  text_align = right
  halign = center
  valign = center
}

# TIME
label {
  monitor = 
  text = cmd[update:1000] echo "$(date +"%H//%M//%S")"
  color = $FCOL1
  font_size = 110
  font_family = $PMF
  shadow_passes = 2
  shadow_size = 2
  shadow_color = rgb(160,160,160)
  position = 0, 70
  text_align = center
  halign = center
  valign = center
}

############################################
#	      CPU info
############################################

# cpu temp / load
label {
  monitor = 
  text = cmd[update:5000] echo "$($CTEMP)°C :CPU Temp<br/>$($LOAD) :CPU Load"
  color = $FCOL1 
  font_size = $SMFS
  font_family = $SMF
  shadow_passes = 2
  shadow_size = 2
  shadow_color = rgb(160,160,160)  
  position = -1120, -120
  text_align = right
  halign = right
  valign = center
}

##########################################
#	       Weather Module
##########################################

# temp in celsius & conditions
label {
  monitor = 
  text = cmd[] echo "Temp: $($WTEMP)°C<br/>Location: $($WTHR)"
  color = $FCOL1
  font_size = $SMFS
  font_family = $SMF
  shadow_passes = 2
  shadow_size = 2
  shadow_color = rgb(160,160,160)  
  position = 1120, -120
  text_align = left
  halign = left
  valign = center
}
