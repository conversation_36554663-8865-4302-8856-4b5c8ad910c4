
$background = rgb(13140d)

$error = rgb(ffb4ab)

$error_container = rgb(c32220)

$inverse_on_surface = rgb(313128)

$inverse_primary = rgb(545c17)

$inverse_surface = rgb(e5e3d6)

$on_background = rgb(e5e3d6)

$on_error = rgb(580003)

$on_error_container = rgb(ffffff)

$on_primary = rgb(262a00)

$on_primary_container = rgb(ffffff)

$on_primary_fixed = rgb(1a1e00)

$on_primary_fixed_variant = rgb(434a04)

$on_secondary = rgb(272a12)

$on_secondary_container = rgb(ffffff)

$on_secondary_fixed = rgb(1b1d07)

$on_secondary_fixed_variant = rgb(45482e)

$on_surface = rgb(e9e7da)

$on_surface_variant = rgb(c8c7b7)

$on_tertiary = rgb(002e25)

$on_tertiary_container = rgb(ffffff)

$on_tertiary_fixed = rgb(002019)

$on_tertiary_fixed_variant = rgb(214d42)

$outline = rgb(979788)

$outline_variant = rgb(656558)

$primary = rgb(c4cd7b)

$primary_container = rgb(616923)

$primary_fixed = rgb(e0e994)

$primary_fixed_dim = rgb(c4cd7b)

$scrim = rgb(000000)

$secondary = rgb(c7c9a7)

$secondary_container = rgb(64674a)

$secondary_fixed = rgb(e3e5c1)

$secondary_fixed_dim = rgb(c7c9a7)

$shadow = rgb(000000)

$source_color = rgb(4d5227)

$surface = rgb(13140d)

$surface_bright = rgb(3e3e35)

$surface_container = rgb(23231b)

$surface_container_high = rgb(2d2e26)

$surface_container_highest = rgb(393930)

$surface_container_low = rgb(1c1d15)

$surface_container_lowest = rgb(0b0c06)

$surface_dim = rgb(13140d)

$surface_tint = rgb(c4cd7b)

$surface_variant = rgb(47483b)

$tertiary = rgb(a2d0c1)

$tertiary_container = rgb(416c60)

$tertiary_fixed = rgb(beecdd)

$tertiary_fixed_dim = rgb(a2d0c1)

