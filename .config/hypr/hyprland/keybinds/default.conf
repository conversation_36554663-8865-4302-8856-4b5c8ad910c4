#! System Controls
##! Audio Volume

bindl = ,XF86AudioMute, exec, wpctl set-volume @DEFAULT_AUDIO_SINK@ 0% # Mute audio
bindl = Super+Shift,M, exec, wpctl set-volume @DEFAULT_AUDIO_SINK@ 0% # Mute audio (alt)
bindle=, XF86AudioRaiseVolume, exec, wpctl set-volume -l 1 @DEFAULT_AUDIO_SINK@ 5%+ # Volume up
bindl  = , XF86AudioMute, exec, agsv1 run-js 'indicator.popup(1);' # [hidden]
bindle=, XF86AudioLowerVolume, exec, wpctl set-volume @DEFAULT_AUDIO_SINK@ 5%- # Volume down

bind = , XF86AudioMicMute, exec, wpctl set-mute @DEFAULT_AUDIO_SOURCE@ toggle # Mute/unmute microphone
bindl  = Super+Shift,M,   exec, agsv1 run-js 'indicator.popup(1);' # Show volume indicator

##! Brightness Controls
bindle=, XF86MonBrightnessUp, exec, agsv1 run-js 'brightness.screen_value += 0.05; indicator.popup(1);' # Brightness up
bindle=, XF86MonBrightnessDown, exec, agsv1 run-js 'brightness.screen_value -= 0.05; indicator.popup(1);' # Brightness down
bindl = Super+CTRL,P, exec,sh ~/.config/ags/scripts/scale.sh 0.05 # Scale up
bindl = Super+CTRL,O, exec,sh ~/.config/ags/scripts/scale.sh -0.05 # Scale down
bindle= Shift,F12, exec,hyprshade toggle blue-light-filter # Toggle blue light filter

#! Essential Controls
##! Launcher and Overview
bind = Super, Tab, exec, pkill rofi || rofi -show drun # App launcher
bindir = Super, Super_L, exec, pkill rofi | agsv1 -t 'overview' # Overview
bind = Super, grave, exec, pkill rofi | agsv1 -t 'glance' # Quick glance
bind = Super, V, exec, pkill rofi || cliphist list | rofi -dmenu | cliphist decode | wl-copy && wtype -M ctrl shift -P v -m ctrl # Clipboard history
bind = Super, Period, exec, pkill rofi || rofi -show emoji # Emoji picker
bind = Super Shift, E, exec, pkill rofi || rofi -show filebrowser # File browser

##! Session Controls
bind = Super, L, exec, loginctl lock-session # Lock screen
bind = Super+Shift, L, exec, loginctl lock-session # Lock screen (alt)
bindl = Super+Shift, L, exec, sleep 0.1 && systemctl suspend || loginctl suspend # Suspend
bind = Ctrl+Super, L, exec, agsv1 run-js 'lock.lock()' # Lock with AGS
bind = Ctrl+Shift+Alt, Delete, exec, pkill wlogout || wlogout -p layer-shell # Logout menu
bind = Ctrl+Shift+Alt+Super, Delete, exec, systemctl poweroff || loginctl poweroff # Power off
bind = Super+Ctrl+Shift, Delete, exec, for ((i=0; i<$(hyprctl monitors -j | jq length); i++)); do agsv1 -t "session""$i"; done # Power menu

#! Window Management
##! Focus Controls
bind = Super, Left, movefocus, l # Focus left
bind = Super, Right, movefocus, r # Focus right
bind = Super, Up, movefocus, u # Focus up
bind = Super, Down, movefocus, d # Focus down
bind = Super, BracketLeft, movefocus, l # Focus left (alt)
bind = Super, BracketRight, movefocus, r # Focus right (alt)
bind = Alt, Tab, cyclenext # Cycle through windows
bind = Alt, Tab, bringactivetotop # Bring active window to top

##! Window Actions
bind = Super, Q, killactive, # Close window
bind = Super+Shift+Alt, Q, exec, hyprctl kill # Pick and kill window
bind = Super, J, togglesplit # Toggle split direction
bind = Super, P, pin # Pin window to all workspaces

#! Window Layout
##! Window Movement
bind = Super+Shift, Left, movewindow, l # Move window left
bind = Super+Shift, Right, movewindow, r # Move window right
bind = Super+Shift, Up, movewindow, u # Move window up
bind = Super+Shift, Down, movewindow, d # Move window down
bindm = Super, mouse:272, movewindow # Move window with mouse
bindm = Super, mouse:273, resizewindow # Resize window with mouse

##! Window Sizing
binde = Super, Minus, splitratio, -0.1 # Decrease split ratio
binde = Super, Equal, splitratio, +0.1 # Increase split ratio
binde = Super, Semicolon, splitratio, -0.1 # Decrease split ratio (alt)
binde = Super, Apostrophe, splitratio, +0.1 # Increase split ratio (alt)

##! Window States
bind = Super+Alt, Space, togglefloating, # Toggle floating
bind = Super+Alt, F, fullscreenstate, 0 3 # Toggle fake fullscreen
bind = Super, F, fullscreen, 0 # Toggle fullscreen
bind = Super, D, fullscreen, 1 # Toggle maximized
bind = Super Ctrl, W, exec, hyprctl dispatch workspaceopt allfloat # Float all windows

#! Workspace Direct Access
##! Switch to Workspace
bind = Super, 1, exec, ~/.config/ags/scripts/hyprland/workspace_action.sh workspace 1 # Switch to workspace 1
bind = Super, 2, exec, ~/.config/ags/scripts/hyprland/workspace_action.sh workspace 2 # Switch to workspace 2
bind = Super, 3, exec, ~/.config/ags/scripts/hyprland/workspace_action.sh workspace 3 # Switch to workspace 3
bind = Super, 4, exec, ~/.config/ags/scripts/hyprland/workspace_action.sh workspace 4 # Switch to workspace 4
bind = Super, 5, exec, ~/.config/ags/scripts/hyprland/workspace_action.sh workspace 5 # Switch to workspace 5
bind = Super, 6, exec, ~/.config/ags/scripts/hyprland/workspace_action.sh workspace 6 # Switch to workspace 6
bind = Super, 7, exec, ~/.config/ags/scripts/hyprland/workspace_action.sh workspace 7 # Switch to workspace 7
bind = Super, 8, exec, ~/.config/ags/scripts/hyprland/workspace_action.sh workspace 8 # Switch to workspace 8
bind = Super, 9, exec, ~/.config/ags/scripts/hyprland/workspace_action.sh workspace 9 # Switch to workspace 9
bind = Super, 0, exec, ~/.config/ags/scripts/hyprland/workspace_action.sh workspace 10 # Switch to workspace 10

##! Move to Workspace
bind = Super+Alt, 1, exec, ~/.config/ags/scripts/hyprland/workspace_action.sh movetoworkspacesilent 1 # Move to workspace 1
bind = Super+Alt, 2, exec, ~/.config/ags/scripts/hyprland/workspace_action.sh movetoworkspacesilent 2 # Move to workspace 2
bind = Super+Alt, 3, exec, ~/.config/ags/scripts/hyprland/workspace_action.sh movetoworkspacesilent 3 # Move to workspace 3
bind = Super+Alt, 4, exec, ~/.config/ags/scripts/hyprland/workspace_action.sh movetoworkspacesilent 4 # Move to workspace 4
bind = Super+Alt, 5, exec, ~/.config/ags/scripts/hyprland/workspace_action.sh movetoworkspacesilent 5 # Move to workspace 5
bind = Super+Alt, 6, exec, ~/.config/ags/scripts/hyprland/workspace_action.sh movetoworkspacesilent 6 # Move to workspace 6
bind = Super+Alt, 7, exec, ~/.config/ags/scripts/hyprland/workspace_action.sh movetoworkspacesilent 7 # Move to workspace 7
bind = Super+Alt, 8, exec, ~/.config/ags/scripts/hyprland/workspace_action.sh movetoworkspacesilent 8 # Move to workspace 8
bind = Super+Alt, 9, exec, ~/.config/ags/scripts/hyprland/workspace_action.sh movetoworkspacesilent 9 # Move to workspace 9
bind = Super+Alt, 0, exec, ~/.config/ags/scripts/hyprland/workspace_action.sh movetoworkspacesilent 10 # Move to workspace 10

#! Workspace Navigation
##! Sequential Navigation
bind = Ctrl+Super, Right, workspace, +1 # Next workspace
bind = Ctrl+Super, Left, workspace, -1 # Previous workspace
bind = Super, Page_Down, workspace, +1 # Next workspace (alt)
bind = Super, Page_Up, workspace, -1 # Previous workspace (alt)
bind = Ctrl+Super, Page_Down, workspace, +1 # Next workspace (alt 2)
bind = Ctrl+Super, Page_Up, workspace, -1 # Previous workspace (alt 2)
bind = Super, mouse_up, workspace, +1 # Next workspace (mouse)
bind = Super, mouse_down, workspace, -1 # Previous workspace (mouse)

##! Advanced Navigation
bind = Ctrl+Super, BracketLeft, workspace, -1 # Previous workspace (bracket)
bind = Ctrl+Super, BracketRight, workspace, +1 # Next workspace (bracket)
bind = Ctrl+Super, Up, workspace, -5 # Jump 5 workspaces back
bind = Ctrl+Super, Down, workspace, +5 # Jump 5 workspaces forward
bind = Ctrl+Super, mouse_up, workspace, +1 # Next workspace (mouse alt)
bind = Ctrl+Super, mouse_down, workspace, -1 # Previous workspace (mouse alt)

##! Special Workspace
bind = Super, S, togglespecialworkspace, # Toggle special workspace
bind = Super, mouse:275, togglespecialworkspace, # Toggle special workspace (mouse)

#! Workspace Window Movement
##! Sequential Movement
bind = Ctrl+Super+Shift, Right, movetoworkspace, +1 # Move to next workspace
bind = Ctrl+Super+Shift, Left, movetoworkspace, -1 # Move to previous workspace
bind = Super+Alt, Page_Down, movetoworkspace, +1 # Move to next workspace (alt)
bind = Super+Alt, Page_Up, movetoworkspace, -1 # Move to previous workspace (alt)
bind = Super+Shift, Page_Down, movetoworkspace, +1 # Move to next workspace (alt 2)
bind = Super+Shift, Page_Up, movetoworkspace, -1 # Move to previous workspace (alt 2)

##! Mouse Movement
bind = Super+Shift, mouse_down, movetoworkspace, -1 # Move to previous workspace (mouse)
bind = Super+Shift, mouse_up, movetoworkspace, +1 # Move to next workspace (mouse)
bind = Super+Alt, mouse_down, movetoworkspace, -1 # Move to previous workspace (mouse alt)
bind = Super+Alt, mouse_up, movetoworkspace, +1 # Move to next workspace (mouse alt)

##! Special Workspace Movement
bind = Super+Alt, S, movetoworkspacesilent, special # Move to special workspace
bind = Ctrl+Super+Shift, Up, movetoworkspacesilent, special # Move to special workspace (alt)

#! Widgets and UI
##! AGS Core Controls
bindr = Ctrl+Super, R, exec, killall agsv1 ; agsv1 -c ~/.config/ags/config.js # Restart widgets
bindr = Ctrl+Super+Alt, R, exec, hyprctl reload; killall agsv1 ydotool; agsv1 & # Reload everything
bind = Super, Z, exec, agsv1 --run-js "globalThis.handleStyles(true)" # Refresh styles

##! Sidebars and Panels
bind = Super, A, exec, pkill rofi | agsv1 -t 'sideleft' # Toggle left sidebar
bind = Super Shift, A, exec, pkill rofi | agsv1 -t 'sideright' # Toggle right sidebar (alt 1)
bind = Super, N, exec, pkill rofi | agsv1 -t 'sideright' # Toggle right sidebar (alt 2)
bind = Super Ctrl, D, exec, for ((i=0; i<$(hyprctl monitors -j | jq length); i++)); do agsv1 -t "desktopbackground""$i"; done # Toggle desktop

#! Widget Tools
##! Widgets
bind = Super, W, exec, pkill rofi | agsv1 -t 'wallselect' # Wallpaper Selector
bind = Super, M, exec, pkill rofi | agsv1 -t 'music' # Music Widget
bind = Super Shift, 3, exec, pkill rofi | agsv1 -t 'music' # Music Widget (alt)
bind = Super, Comma, exec, agsv1 run-js 'openColorScheme.value = true; Utils.timeout(1500, () => openColorScheme.value = false);' # Color scheme
bind = Super Shift, 1, exec, agsv1 run-js 'openColorScheme.value = true; Utils.timeout(1500, () => openColorScheme.value = false);' # Color scheme (alt)
bind = Super, Slash, exec, agsv1 -t 'cheatsheet0' # Keybinding cheatsheet
bind = Super Shift, 2, exec, pkill rofi | agsv1 -t 'recorder' # Toggle recorder (alt 1)
bind = Super Shift, R, exec, agsv1 -t 'recorder' # Toggle recorder (alt 2)

##! Bar Mode Controls
bind = Super, X, exec, agsv1 --run-js 'const current = parseInt(currentShellMode.value[0]); const next = current >= 0 && current < 8 ? current + 1 : 0; updateMonitorShellMode(currentShellMode, 0, next.toString())' # Cycle horizontal bars
bind = Super Shift, X, exec, agsv1 --run-js 'const current = parseInt(currentShellMode.value[0]); const next = current >= 9 && current < 10 ? current + 1 : 9; updateMonitorShellMode(currentShellMode, 0, next.toString())' # Cycle vertical bars
bind = Super Alt, X, exec, agsv1 --run-js 'toggleBarPosition()' # Toggle bar position

#! Applications
##! Terminal and File Management
bind = Super, T, exec, warp # Launch Terminal (primary)
bind = Super, Return, exec, ghostty # Launch Terminal (secondary)
bind = Super, E, exec, nautilus --new-window # File Manager

##! Browsers and Development
bind = Super, B, exec, zen-browser # Launch Browser
bind = Super, C, exec, code # Launch Code
bind = Super + Shift, V, exec, ghostty -e nvim # Launch Neovim
bind = Super Shift, G, exec, github-desktop # Launch GitHub Desktop

#! Productivity and Tools
##! Productivity Apps
bind = Super, O, exec, obsidian # Launch Obsidian
bind = Super, I, exec, XDG_CURRENT_DESKTOP="gnome" gnome-control-center # Launch Settings
bind = Ctrl+Super, V, exec, pavucontrol # Volume Mixer
bind = Ctrl+Shift, Escape, exec, ghostty -e btop # System Monitor
bind = Super CTRL, S, exec, spotify # Launch Spotify

#! Media Controls
##! Media Playback
bindl= Super+Shift, P, exec, playerctl play-pause # Play/Pause
bindl= ,XF86AudioPlay, exec, playerctl play-pause # Play/Pause (media key)
bindl= ,XF86AudioPause, exec, playerctl play-pause # Pause (media key)
bindl= Super+Shift, N, exec, playerctl next || playerctl position `bc <<< "100 * $(playerctl metadata mpris:length) / 1000000 / 100"` # Next track
bindl= ,XF86AudioNext, exec, playerctl next || playerctl position `bc <<< "100 * $(playerctl metadata mpris:length) / 1000000 / 100"` # Next track (media key)
bindl= Super+Shift, B, exec, playerctl previous # Previous track
bindl= ,XF86AudioPrev, exec, playerctl previous # Previous track (media key)
bind = Super+Shift+Alt, mouse:275, exec, playerctl previous # Previous track (mouse)
bind = Super+Shift+Alt, mouse:276, exec, playerctl next || playerctl position `bc <<< "100 * $(playerctl metadata mpris:length) / 1000000 / 100"` # Next track (mouse)

#! Screenshots
##! Screenshot Capture
bindl=, Print, exec, grim - | wl-copy # Screenshot to clipboard
bindl= Ctrl, Print, exec, mkdir -p ~/Pictures/Screenshots && ~/.config/ags/scripts/grimblast.sh copysave screen ~/Pictures/Screenshots/Screenshot_"$(date '+%Y-%m-%d_%H.%M.%S')".png # Screenshot to file
bind = Super+Shift, S, exec, ~/.config/ags/scripts/grimblast.sh --freeze copy area # Screen snip to clipboard
bind = Super+Shift+Alt, S, exec, grim -g "$(slurp)" - | swappy -f - # Screen snip to editor

##! OCR and Recording
bind = Super+Shift, T, exec, grim -g "$(slurp $SLURP_ARGS)" "tmp.png" && tesseract -l eng "tmp.png" - | wl-copy && rm "tmp.png" # Screen OCR
bind = Ctrl+Super+Shift, S, exec, grim -g "$(slurp $SLURP_ARGS)" "tmp.png" && tesseract "tmp.png" - | wl-copy && rm "tmp.png" # Screen OCR (alt)
bind = Super+Alt, R, exec, ~/.config/ags/scripts/record-script.sh # Record region
bind = Ctrl+Alt, R, exec, ~/.config/ags/scripts/record-script.sh --fullscreen # Record full screen
bind = Super+Shift+Alt, R, exec, ~/.config/ags/scripts/record-script.sh --fullscreen-sound # Record with audio

#! Utilities and Testing
##! Utilities
bind = Super+Shift, C, exec, hyprpicker -a # Color picker
bind = Super, R, exec, sh ~/.config/ags/scripts/color_generation/wallpapers.sh -r # Random wallpaper

##! Testing
bind = SuperAlt, f12, exec, notify-send "Hyprland version: $(hyprctl version | head -2 | tail -1 | cut -f2 -d ' ')" "owo" -a 'Hyprland keybind' # Show version
bind = Super+Alt, f11, exec, notify-send "Millis since epoch" "$(date +%s%N | cut -b1-13)" -a 'Hyprland keybind' # Show time
bind = Super+Alt, f10, exec, notify-send 'Test notification' "Here's a really long message to test truncation and wrapping\nYou can middle click or flick this notification to dismiss it!" -a 'Shell' -A "Test1=I got it!" -A "Test2=Another action" -t 5000 # Test notification
bind = Super+Alt, Equal, exec, notify-send "Urgent notification" "Ah hell no" -u critical -a 'Hyprland keybind' # Test urgent notification 