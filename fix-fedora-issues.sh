#!/bin/bash
# Quick fix for Fedora installation issues

print_info() {
    echo -e "\e[32mINFO:\e[0m $1"
}

print_warning() {
    echo -e "\e[33mWARNING:\e[0m $1"
}

print_error() {
    echo -e "\e[31mERROR:\e[0m $1" >&2
}

print_step() {
    echo -e "\n\e[34m-->\e[0m \e[1m$1\e[0m"
}

print_step "Fixing Fedora Installation Issues"

# Fix 1: Install missing packages with correct names
print_info "Installing missing packages with correct names..."

# Install starship
if ! command -v starship &>/dev/null; then
    print_info "Installing starship..."
    curl -sS https://starship.rs/install.sh | sh -s -- -y
    # Add to PATH
    if ! echo "$PATH" | grep -q "$HOME/.local/bin"; then
        echo 'export PATH="$HOME/.local/bin:$PATH"' >> ~/.bashrc
    fi
fi

# Install polkit-gnome
print_info "Installing polkit authentication agent..."
sudo dnf install -y polkit-gnome || {
    print_warning "polkit-gnome not available, trying alternatives..."
    sudo dnf install -y lxpolkit || {
        sudo dnf install -y polkit-kde-agent-1 || {
            print_warning "No polkit agent found, you may need to install one manually"
        }
    }
}

# Install blueberry or alternative
print_info "Installing bluetooth manager..."
sudo dnf install -y blueberry || {
    print_warning "blueberry not available, using gnome-bluetooth"
}

# Install python3-setuptools-scm
print_info "Installing Python setuptools-scm..."
sudo dnf install -y python3-setuptools-scm || {
    print_warning "python3-setuptools-scm not available via DNF, installing via pip..."
    pip3 install --user setuptools-scm || print_warning "Failed to install setuptools-scm"
}

# Fix 2: Handle power-profiles-daemon conflict
print_info "Fixing power-profiles-daemon conflict..."
if rpm -q tuned-ppd &>/dev/null; then
    print_info "Removing tuned-ppd to resolve conflict..."
    sudo dnf remove -y tuned-ppd || print_warning "Failed to remove tuned-ppd"
    sudo dnf install -y power-profiles-daemon || print_warning "Failed to install power-profiles-daemon"
fi

# Fix 3: Install correct font packages
print_info "Installing correct font packages..."
sudo dnf install -y google-noto-fonts google-noto-emoji-fonts google-noto-sans-cjk-fonts || {
    print_warning "Some font packages failed to install"
}

# Fix 4: Download Material Symbols fonts properly
print_step "Fixing Material Symbols Fonts"

mkdir -p ~/.local/share/fonts

print_info "Downloading Material Symbols fonts directly..."

# Download fonts with proper error handling
fonts_urls=(
    "https://github.com/google/material-design-icons/raw/master/variablefont/MaterialSymbolsOutlined%5BFILL%2CGRAD%2Copsz%2Cwght%5D.ttf"
    "https://github.com/google/material-design-icons/raw/master/variablefont/MaterialSymbolsRounded%5BFILL%2CGRAD%2Copsz%2Cwght%5D.ttf"
    "https://github.com/google/material-design-icons/raw/master/variablefont/MaterialSymbolsSharp%5BFILL%2CGRAD%2Copsz%2Cwght%5D.ttf"
)

font_names=(
    "MaterialSymbolsOutlined.ttf"
    "MaterialSymbolsRounded.ttf"
    "MaterialSymbolsSharp.ttf"
)

for i in "${!fonts_urls[@]}"; do
    url="${fonts_urls[$i]}"
    name="${font_names[$i]}"
    
    if [ ! -f ~/.local/share/fonts/"$name" ]; then
        print_info "Downloading $name..."
        if curl -L "$url" -o ~/.local/share/fonts/"$name"; then
            print_info "✓ Downloaded $name"
        else
            print_warning "✗ Failed to download $name"
        fi
    else
        print_info "✓ $name already exists"
    fi
done

# Refresh font cache
print_info "Refreshing font cache..."
fc-cache -fv

# Fix 5: Install additional required packages
print_info "Installing additional packages that might be missing..."

additional_packages=(
    "rofi-wayland"
    "foot"
    "fish"
    "cava"
    "tesseract"
    "tesseract-langpack-eng"
)

for package in "${additional_packages[@]}"; do
    if ! rpm -q "$package" &>/dev/null; then
        print_info "Installing $package..."
        sudo dnf install -y "$package" || print_warning "Failed to install $package"
    fi
done

# Fix 6: Create fontconfig configuration for Material Symbols
print_info "Creating fontconfig configuration..."
mkdir -p ~/.config/fontconfig/conf.d

cat > ~/.config/fontconfig/conf.d/99-material-symbols.conf << 'EOF'
<?xml version="1.0"?>
<!DOCTYPE fontconfig SYSTEM "fonts.dtd">
<fontconfig>
  <alias>
    <family>Material Symbols Outlined</family>
    <prefer>
      <family>Material Symbols Outlined</family>
    </prefer>
  </alias>
  
  <alias>
    <family>Material Icons</family>
    <prefer>
      <family>Material Symbols Outlined</family>
      <family>Material Symbols Rounded</family>
    </prefer>
  </alias>
</fontconfig>
EOF

print_step "Verification"

print_info "Checking installation status..."

# Check fonts
if fc-list | grep -qi "material"; then
    print_info "✓ Material fonts are installed"
else
    print_warning "✗ Material fonts not found"
fi

# Check key commands
commands=("starship" "git" "hyprland")
for cmd in "${commands[@]}"; do
    if command -v "$cmd" &>/dev/null; then
        print_info "✓ $cmd is available"
    else
        print_warning "✗ $cmd is missing"
    fi
done

print_step "Completion"

print_info "Fix script completed!"
print_info ""
print_info "What was fixed:"
print_info "• Installed missing packages with correct names"
print_info "• Resolved power-profiles-daemon conflict"
print_info "• Downloaded Material Symbols fonts properly"
print_info "• Created fontconfig configuration"
print_info "• Installed additional required packages"
print_info ""
print_info "Next steps:"
print_info "1. Restart your terminal: source ~/.bashrc"
print_info "2. Continue with the installation: ./installer-fedora.sh"
print_info "3. Or run the main installer: ./installer.sh"
print_info ""
print_warning "Note: You may need to restart your terminal or log out/in for all changes to take effect."
