#!/bin/bash
# by SnowF & Gemini
# Exit immediately if a command exits with a non-zero status.
set -e

# --- Configuration ---
REPO_URL="https://github.com/Lunaris-Project/HyprLuna.git"
AGSV1_REPO_URL="https://github.com/Lunaris-Project/agsv1"
HYPRLUNA_DIR="$HOME/HyprLuna"
BACKUP_DIR="$HOME/HyprLuna-User-Bak"
PARU_TEMP_DIR=$(mktemp -d)  # Temporary directory for building paru
AGSV1_TEMP_DIR=$(mktemp -d) # Temporary directory for building agsv1

# List of packages to install via paru (Arch/AUR packages)
ARCH_PACKAGES=(
    git hyprland axel bc coreutils cliphist cmake curl rofi-wayland rsync wget ripgrep jq npm meson typescript gjs xdg-user-dirs
    brightnessctl ddcutil pavucontrol wireplumber libdbusmenu-gtk3 kitty playerctl swww gobject-introspection glib2-devel gvfs glib2
    glibc gtk3 gtk-layer-shell libpulse pam gnome-bluetooth-3.0 gammastep libsoup3 libnotify networkmanager power-profiles-daemon
    upower adw-gtk-theme-git qt5ct qt5-wayland fontconfig ttf-readex-pro ttf-jetbrains-mono-nerd ttf-material-symbols-variable-git
    apple-fonts ttf-space-mono-nerd ttf-rubik-vf bibata-cursor-theme-bin bibata-rainbow-cursor-theme bibata-extra-cursor-theme
    bibata-cursor-translucent ttf-gabarito-git fish foot starship polkit-gnome gnome-keyring gnome-control-center
    blueberry webp-pixbuf-loader gtksourceview3 yad ydotool xdg-user-dirs-gtk tinyxml2 gtkmm3 gtksourceviewmm cairomm xdg-desktop-portal
    xdg-desktop-portal-gtk xdg-desktop-portal-hyprland gradience python-libsass python-pywalfox matugen-bin python-build python-pillow
    python-pywal python-setuptools-scm python-wheel swappy wf-recorder grim tesseract tesseract-data-eng slurp dart-sass python-pywayland
    python-psutil hypridle hyprutils hyprlock wlogout wl-clipboard hyprpicker ghostty ttf-noto-sans-cjk-vf noto-fonts-emoji cava metar
    ttf-material-symbols-variable-git gowall go overskride visual-studio-code-bin mpv github-desktop-bin
)

# List of packages for Fedora (DNF packages)
FEDORA_PACKAGES=(
    git hyprland axel bc coreutils cmake curl rofi-wayland rsync wget ripgrep jq npm meson typescript gjs xdg-user-dirs
    brightnessctl ddcutil pavucontrol wireplumber libdbusmenu-gtk3-devel kitty playerctl gobject-introspection-devel glib2-devel gvfs glib2
    glibc gtk3-devel libpulse-devel pam-devel gnome-bluetooth gammastep libsoup3-devel libnotify-devel NetworkManager power-profiles-daemon
    upower qt5ct qt5-qtwayland fontconfig jetbrains-mono-fonts-all fish foot starship polkit-gnome gnome-keyring gnome-control-center
    blueberry webp-pixbuf-loader gtksourceview3-devel yad xdg-user-dirs-gtk tinyxml2-devel gtkmm30-devel gtksourceviewmm3-devel cairomm-devel
    xdg-desktop-portal xdg-desktop-portal-gtk python3-libsass python3-build python3-pillow python3-setuptools-scm python3-wheel
    tesseract tesseract-langpack-eng python3-psutil wl-clipboard noto-fonts-cjk-vf noto-fonts-emoji cava golang mpv
)

# Packages that need special handling (AUR/source builds for Fedora)
FEDORA_SPECIAL_PACKAGES=(
    cliphist swww hyprutils hypridle hyprlock wlogout hyprpicker gradience matugen python-pywal python-pywalfox
    swappy wf-recorder grim slurp dart-sass python-pywayland gowall overskride ghostty
)

# Set the appropriate package list based on distribution
if [ "$IS_ARCH" = true ]; then
    REQUIRED_PACKAGES=("${ARCH_PACKAGES[@]}")
else
    REQUIRED_PACKAGES=("${FEDORA_PACKAGES[@]}")
fi

# Directories that will be copied from the HyprLuna repo to your home directory
# !! WARNING !! These will overwrite existing files/directories in your home folder.
DOTFILES_DIRS=(".config" ".local" ".cache" ".vscode" ".fonts" ".ags" "Pictures") # Pictures is risky!

# --- Functions ---

# Function to display messages in color
print_step() {
    echo -e "\n\e[34m-->\e[0m \e[1m$1\e[0m"
}

print_info() {
    echo -e "\e[32mINFO:\e[0m $1"
}

print_warning() {
    echo -e "\e[33mWARNING:\e[0m $1"
}

print_error() {
    echo -e "\e[31mERROR:\e[0m $1" >&2
}

# Function to install special packages for Fedora
install_fedora_special_packages() {
    print_info "Installing Material Symbols fonts..."

    # Create fonts directory
    mkdir -p ~/.local/share/fonts

    # Download and install Material Symbols fonts
    if ! fc-list | grep -q "Material Symbols"; then
        print_info "Downloading Material Symbols fonts from Google Fonts..."
        curl -L "https://github.com/google/material-design-icons/raw/master/variablefont/MaterialSymbolsOutlined%5BFILL%2CGRAD%2Copsz%2Cwght%5D.ttf" \
             -o ~/.local/share/fonts/MaterialSymbolsOutlined.ttf || {
            print_warning "Failed to download Material Symbols Outlined font"
        }
        curl -L "https://github.com/google/material-design-icons/raw/master/variablefont/MaterialSymbolsRounded%5BFILL%2CGRAD%2Copsz%2Cwght%5D.ttf" \
             -o ~/.local/share/fonts/MaterialSymbolsRounded.ttf || {
            print_warning "Failed to download Material Symbols Rounded font"
        }
        curl -L "https://github.com/google/material-design-icons/raw/master/variablefont/MaterialSymbolsSharp%5BFILL%2CGRAD%2Copsz%2Cwght%5D.ttf" \
             -o ~/.local/share/fonts/MaterialSymbolsSharp.ttf || {
            print_warning "Failed to download Material Symbols Sharp font"
        }

        # Refresh font cache
        fc-cache -fv
        print_info "Material Symbols fonts installed successfully."
    else
        print_info "Material Symbols fonts already installed."
    fi

    # Install additional fonts
    print_info "Installing additional fonts..."
    sudo dnf install -y google-noto-fonts google-noto-emoji-fonts google-noto-sans-cjk-fonts \
                        jetbrains-mono-fonts-all fira-code-fonts || {
        print_warning "Some fonts failed to install"
    }

    # Install packages that can be built from source
    local build_dir=$(mktemp -d)
    cd "$build_dir"

    # Install cliphist
    if ! command -v cliphist &>/dev/null; then
        print_info "Building cliphist from source..."
        git clone https://github.com/sentriz/cliphist.git
        cd cliphist
        make install PREFIX=~/.local || print_warning "Failed to build cliphist"
        cd ..
    fi

    # Install swww
    if ! command -v swww &>/dev/null; then
        print_info "Building swww from source..."
        git clone https://github.com/Horus645/swww.git
        cd swww
        cargo build --release
        cp target/release/swww ~/.local/bin/ || print_warning "Failed to build swww"
        cp target/release/swww-daemon ~/.local/bin/ || print_warning "Failed to build swww-daemon"
        cd ..
    fi

    # Install grim and slurp (screenshot tools)
    if ! command -v grim &>/dev/null; then
        print_info "Building grim from source..."
        git clone https://github.com/emersion/grim.git
        cd grim
        meson build
        ninja -C build
        sudo ninja -C build install || print_warning "Failed to build grim"
        cd ..
    fi

    if ! command -v slurp &>/dev/null; then
        print_info "Building slurp from source..."
        git clone https://github.com/emersion/slurp.git
        cd slurp
        meson build
        ninja -C build
        sudo ninja -C build install || print_warning "Failed to build slurp"
        cd ..
    fi

    cd - >/dev/null
    rm -rf "$build_dir"

    print_info "Special packages installation completed."
}

# --- Script Start ---

print_step "Starting HyprLuna Installation Script"

# Check if running as root
if [ "$EUID" -eq 0 ]; then
    print_error "This script should be run as a normal user, not root."
    print_error "The script will use 'sudo' internally where needed."
    exit 1
fi

# Detect distribution
DISTRO_ID=$(grep "^ID=" /etc/os-release | cut -d'=' -f2 | tr -d '"')
IS_ARCH=false
IS_FEDORA=false

case "$DISTRO_ID" in
    arch|manjaro|endeavouros|cachyos|arcolinux)
        IS_ARCH=true
        print_info "Detected Arch-based distribution: $DISTRO_ID"
        ;;
    fedora|nobara)
        IS_FEDORA=true
        print_info "Detected Fedora-based distribution: $DISTRO_ID"
        ;;
    *)
        print_warning "This script is designed for Arch Linux and Fedora-based distributions."
        print_warning "Detected: $DISTRO_ID - It may not work correctly on other systems."
        read -p "Do you want to continue anyway? (y/n): " consent < /dev/tty
        if [ "$consent" != "y" ]; then
            print_info "Installation aborted by user."
            exit 1
        fi
        ;;
esac

# Check if git is installed (prerequisite)
if ! command -v git &>/dev/null; then
    print_error "Git is required but not installed."
    if [ "$IS_ARCH" = true ]; then
        print_error "Please install git first using: sudo pacman -S git"
    elif [ "$IS_FEDORA" = true ]; then
        print_error "Please install git first using: sudo dnf install git"
    else
        print_error "Please install git using your distribution's package manager"
    fi
    exit 1
fi

# --- Step 1: Install Package Manager and Development Tools ---
if [ "$IS_ARCH" = true ]; then
    print_step "Step 1: Installing AUR Helper (paru)"

    # Clean up temporary directories on script exit
    trap 'rm -rf "$PARU_TEMP_DIR" "$AGSV1_TEMP_DIR"' EXIT

    # Check if paru is already installed
    if command -v paru &>/dev/null; then
        print_info "paru is already installed. Skipping paru installation."
    else
        print_info "Installing base-devel package group..."
        sudo pacman -S --needed base-devel --noconfirm || {
            print_error "Failed to install base-devel."
            exit 1
        }

        print_info "Cloning paru repository..."
        git clone https://aur.archlinux.org/paru.git "$PARU_TEMP_DIR/paru" || {
            print_error "Failed to clone paru repository."
            exit 1
        }

        print_info "Building and installing paru..."
        cd "$PARU_TEMP_DIR/paru" || {
            print_error "Failed to change directory to paru temp dir."
            exit 1
        }
        makepkg -si --noconfirm || {
            print_error "Failed to build and install paru. Please check the output above."
            exit 1
        }
        cd - >/dev/null # Go back to the previous directory

        print_info "paru installed successfully."
    fi
elif [ "$IS_FEDORA" = true ]; then
    print_step "Step 1: Installing Development Tools for Fedora"

    # Clean up temporary directories on script exit
    trap 'rm -rf "$AGSV1_TEMP_DIR"' EXIT

    print_info "Installing development tools and RPM Fusion repositories..."

    # Install development tools
    sudo dnf groupinstall -y "Development Tools" "C Development Tools and Libraries" || {
        print_warning "Failed to install some development tools, continuing..."
    }

    # Enable RPM Fusion repositories for additional packages
    sudo dnf install -y https://mirrors.rpmfusion.org/free/fedora/rpmfusion-free-release-$(rpm -E %fedora).noarch.rpm \
                        https://mirrors.rpmfusion.org/nonfree/fedora/rpmfusion-nonfree-release-$(rpm -E %fedora).noarch.rpm || {
        print_warning "Failed to install RPM Fusion repositories, some packages may not be available."
    }

    # Install Rust (needed for building some packages)
    if ! command -v cargo &>/dev/null; then
        print_info "Installing Rust toolchain..."
        curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y
        source ~/.cargo/env
    fi

    print_info "Development environment setup completed."
fi


# --- Step 2: Install Required Packages ---
if [ "$IS_ARCH" = true ]; then
    print_step "Step 2: Installing Required Packages via paru"

    # Check if paru is usable now
    if ! command -v paru &>/dev/null; then
        print_error "paru command not found after installation attempt."
        print_error "Please install paru manually and re-run the script."
        exit 1
    fi

    # The main command for the silent installation attempt
    PARU_INSTALL_CMD="paru -S --needed ${REQUIRED_PACKAGES[@]} --noconfirm"

    print_info "Using paru to install necessary system and AUR packages."
    print_info "You will likely be prompted for your password and asked to confirm installations."

    # Attempt the installation silently first using --noconfirm
    if ! $PARU_INSTALL_CMD; then
        print_warning "Package installation failed!"
        print_warning "This often happens due to conflicting packages or dependency issues."

        # Loop to present options to the user until a valid choice is made
        while true; do
            print_info "Please choose how to proceed:"
            print_info "  1) Retry installation interactively (recommended)."
            print_info "  2) Skip package installation step (NOT recommended)."
            print_info "  3) Exit the script."
            read -p "Enter choice [1]: " choice < /dev/tty
            choice=${choice:-1}

            case "$choice" in
                1)
                    print_info "Retrying package installation interactively..."
                    paru -S --needed "${REQUIRED_PACKAGES[@]}"
                    break
                    ;;
                2)
                    print_warning "Skipping package installation step as requested."
                    break
                    ;;
                3)
                    print_info "Exiting script at package installation step as requested."
                    exit 1
                    ;;
                *)
                    print_warning "Invalid choice. Please enter 1, 2, or 3."
                    ;;
            esac
        done
    fi

elif [ "$IS_FEDORA" = true ]; then
    print_step "Step 2: Installing Required Packages for Fedora"

    print_info "Installing packages via DNF..."
    sudo dnf install -y "${REQUIRED_PACKAGES[@]}" || {
        print_warning "Some packages failed to install via DNF. This is expected for some packages."
    }

    print_info "Installing special packages that need to be built from source..."
    install_fedora_special_packages
fi

print_info "Required packages process completed."

# --- Step 3: Install Latest AGS v1 ---
print_step "Step 3: Installing Latest AGS v1"

# Check if ags is already installed and seems recent enough (basic check)
if command -v ags &>/dev/null; then
    print_info "AGS seems to be installed. Skipping AGS v1 build."
    print_warning "Note: The script did not verify the specific version. If you encounter issues, consider removing and rebuilding AGS v1 manually."
else
    print_info "Installing typescript globally via npm..."
    # Check if npm is available, if not, it should have been installed in step 2.
    if ! command -v npm &>/dev/null; then
        print_error "npm command not found. It should have been installed in Step 2. Please check package installation."
        exit 1
    fi
    sudo npm i -g typescript || {
        print_error "Failed to install typescript globally."
        exit 1
    }

    print_info "Cloning agsv1 repository..."
    git clone --recursive "$AGSV1_REPO_URL" "$AGSV1_TEMP_DIR/agsv1" || {
        print_error "Failed to clone agsv1 repository."
        exit 1
    }

    print_info "Building and installing agsv1..."
    cd "$AGSV1_TEMP_DIR/agsv1" || {
        print_error "Failed to change directory to agsv1 temp dir."
        exit 1
    }

    if [ "$IS_ARCH" = true ]; then
        makepkg -si --noconfirm || {
            print_error "Failed to build and install agsv1. Please check the output above."
            exit 1
        }
    elif [ "$IS_FEDORA" = true ]; then
        # For Fedora, we need to build manually
        print_info "Installing additional dependencies for AGS v1 on Fedora..."
        sudo dnf install -y gtk3-devel gtk-layer-shell-devel libpulse-devel \
                            gobject-introspection-devel gjs-devel || {
            print_warning "Some AGS dependencies failed to install"
        }

        # Build AGS v1
        meson build --prefix=/usr/local
        ninja -C build
        sudo ninja -C build install || {
            print_error "Failed to build and install agsv1. Please check the output above."
            exit 1
        }

        # Add /usr/local/bin to PATH if not already there
        if ! echo "$PATH" | grep -q "/usr/local/bin"; then
            echo 'export PATH="/usr/local/bin:$PATH"' >> ~/.bashrc
            export PATH="/usr/local/bin:$PATH"
        fi
    fi

    cd - >/dev/null # Go back to the previous directory

    print_info "AGS v1 installed successfully."
fi

# --- Step 4: Create Backup (Optional) ---
print_step "Step 4: Creating Backup of Existing Configuration (Optional)"

read -p "Do you want to create a backup of your existing configuration directories? (y/n): " backup_consent < /dev/tty
if [ "$backup_consent" = "y" ]; then
    print_info "Creating backup in $BACKUP_DIR..."
    mkdir -p "$BACKUP_DIR" || {
        print_error "Failed to create backup directory $BACKUP_DIR."
        exit 1
    }

    for dir in "${DOTFILES_DIRS[@]}"; do
        if [ -d "$HOME/$dir" ]; then
            print_info "Backing up $HOME/$dir..."
            cp -r "$HOME/$dir" "$BACKUP_DIR/"
        else
            print_warning "Directory $HOME/$dir not found, skipping backup."
        fi
    done
    print_info "Backup complete. Your original configurations are in $BACKUP_DIR"
else
    print_info "Skipping backup step."
fi

# --- Step 5: Clone and Setup HyprLuna ---
print_step "Step 5: Cloning and Setting up HyprLuna Dotfiles"

print_warning "========================================================="
print_warning "  CRITICAL STEP: OVERWRITING EXISTING CONFIGURATIONS!"
print_warning "========================================================="
print_warning "This step will copy HyprLuna dotfiles from the cloned repository"
print_warning "into your home directory (~/). This will OVERWRITE existing files"
print_warning "and directories such as: ~/.config, ~/.local, ~/.cache, ~/.vscode,"
print_warning "~/.fonts, ~/.ags, and potentially files in your ~/Pictures directory."
print_warning "If you have important configurations in these directories that you haven't"
print_warning "backed up elsewhere (beyond the optional script backup), ABORT NOW."
print_warning "Existing directories will be renamed with a '.bak_before_install' suffix as a safety measure."
print_warning ""
read -p "Are you absolutely sure you want to proceed and overwrite? (y/n): " overwrite_consent < /dev/tty

if [ "$overwrite_consent" != "y" ]; then
    print_info "Installation aborted by user before copying dotfiles."
    exit 1
fi

print_info "Cloning HyprLuna repository..."
# Remove existing clone directory if it exists, to ensure a fresh clone
if [ -d "$HYPRLUNA_DIR" ]; then
    print_info "Removing existing HyprLuna clone directory: $HYPRLUNA_DIR"
    rm -rf "$HYPRLUNA_DIR" || {
        print_error "Failed to remove existing HyprLuna clone directory."
        exit 1
    }
fi
git clone "$REPO_URL" "$HYPRLUNA_DIR" || {
    print_error "Failed to clone HyprLuna repository."
    exit 1
}

print_info "Copying dotfiles from $HYPRLUNA_DIR to your home directory..."
cd "$HYPRLUNA_DIR" || {
    print_error "Failed to change directory to HyprLuna repository."
    exit 1
}

for dir in "${DOTFILES_DIRS[@]}"; do
    if [ -d "./$dir" ]; then # Check if the directory exists in the cloned repo
        print_info "Processing $dir..."
        if [ -d "$HOME/$dir" ]; then
            print_warning "Renaming existing $HOME/$dir to $HOME/${dir}.bak_before_install..."
            mv "$HOME/$dir" "$HOME/${dir}.bak_before_install" || {
                print_error "Failed to rename existing $HOME/$dir."
                continue
            }
        fi
        print_info "Copying $dir to $HOME/..."
        # The original command is cp -r ./Pictures ~/ which copies the Pictures *directory*
        # from the repo into the home directory. This might result in ~/Pictures/Pictures.
        # Let's stick to the source command but ensure the old one is backed up.
        cp -r "./$dir" "$HOME/" || {
            print_error "Failed to copy $dir."
            continue
        }
        print_info "$dir copied."
    else
        print_warning "Directory ./$dir not found in the cloned repository, skipping copy."
    fi
done

print_info "Setting execute permissions for scripts..."
chmod +x "$HOME"/.config/hypr/scripts/* || print_warning "Failed to set execute permissions for hypr/scripts."
chmod +x "$HOME"/.config/ags/scripts/hyprland/* || print_warning "Failed to set execute permissions for ags/scripts/hyprland."

print_info "Running wallpaper generation script..."
sh "$HOME"/.config/ags/scripts/color_generation/wallpapers.sh -r || print_warning "Wallpaper script failed. You may need to run this manually."

cd - >/dev/null # Go back to the original directory

print_info "HyprLuna dotfiles copied and initial setup steps completed."

# --- Post-Installation Instructions ---
print_step "HyprLuna Installation Script Finished"

print_info "Installation is mostly complete."
print_info "You need to log out of your current session and log back in."
print_info "At the login screen (greeter), select 'Hyprland' as your session."
print_info "Then log in with your username and password."

if [ "$backup_consent" = "y" ]; then
    print_info "Your original configurations were backed up to: $BACKUP_DIR"
fi

print_info "If Hyprland doesn't start or you encounter issues, you can check logs:"
print_info "cat ~/.local/share/hyprland/hyprland.log"
print_info "Refer to our discord server: https://discord.gg/qnAHD9keWr for help."
print_info "After logging in, you can explore the HyprLuna setup!"

exit 0 # Script finished successfully
