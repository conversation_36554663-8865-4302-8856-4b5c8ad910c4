# HyprLuna Installation Guide for Fedora

This guide will help you install HyprLuna dotfiles on Fedora 42 and other Fedora-based systems like Nobara.

## Prerequisites

Before starting, make sure you have:
- Fedora 42 or compatible system
- Hyprland already installed and working
- Internet connection
- Sudo privileges

## Known Issues and Solutions

### Material Icons Issue
The main issue reported is that Material Symbols icons don't display properly on Fedora. This installer addresses this by:
1. Downloading Material Symbols fonts directly from Google Fonts
2. Installing them in the correct location (`~/.local/share/fonts`)
3. Refreshing the font cache properly

### AGS v1 Installation
AGS v1 requires specific glib dependencies that are handled differently on Fedora:
- Installing proper development headers
- Building from source with correct meson configuration
- Setting up proper PATH variables

## Installation Steps

### Step 1: Prepare Your System

First, make sure your system is up to date:
```bash
sudo dnf update -y
```

### Step 2: Run the Fedora-Specific Pre-installer

This script will install all the necessary dependencies and build tools:

```bash
chmod +x installer-fedora.sh
./installer-fedora.sh
```

This script will:
- Install development tools and RPM Fusion repositories
- Install Rust and Go toolchains
- Install all available packages via DNF
- Download and install Material Symbols fonts properly
- Build missing packages from source (cliphist, swww, grim, slurp, etc.)

### Step 3: Install AGS v1

After the pre-installer completes, install AGS v1:

```bash
# Source your bashrc to get updated PATH
source ~/.bashrc

# Clone and build AGS v1
git clone --recursive https://github.com/Lunaris-Project/agsv1 /tmp/agsv1
cd /tmp/agsv1

# Install additional dependencies
sudo dnf install -y gtk3-devel gtk-layer-shell-devel libpulse-devel \
                    gobject-introspection-devel gjs-devel

# Build and install
meson build --prefix=/usr/local
ninja -C build
sudo ninja -C build install

# Clean up
cd ~
rm -rf /tmp/agsv1
```

### Step 4: Install HyprLuna Dotfiles

Now you can run the main installer (with modifications for Fedora):

```bash
# Make sure the installer is executable
chmod +x installer.sh

# Run the installer
./installer.sh
```

The installer will detect Fedora and use the appropriate package manager and paths.

## Post-Installation

### 1. Restart Your Session
Log out and log back in, selecting Hyprland as your session.

### 2. Verify Installation
Check that key components are working:

```bash
# Check if AGS is working
ags --version

# Check if fonts are installed
fc-list | grep -i "material"

# Check if key binaries are available
which swww cliphist grim slurp
```

### 3. Fix Common Issues

#### If Material Icons Still Don't Show:
```bash
# Refresh font cache
fc-cache -fv

# Check font installation
fc-list | grep -i material

# If fonts are missing, reinstall them:
mkdir -p ~/.local/share/fonts
wget -O ~/.local/share/fonts/MaterialSymbolsOutlined.ttf \
  "https://github.com/google/material-design-icons/raw/master/variablefont/MaterialSymbolsOutlined%5BFILL%2CGRAD%2Copsz%2Cwght%5D.ttf"
fc-cache -fv
```

#### If AGS Doesn't Start:
```bash
# Check if AGS is in PATH
which ags

# If not found, add to PATH
echo 'export PATH="/usr/local/bin:$PATH"' >> ~/.bashrc
source ~/.bashrc

# Check AGS dependencies
ldd /usr/local/bin/ags
```

#### If Hyprland Doesn't Start:
Check the Hyprland log:
```bash
cat ~/.local/share/hyprland/hyprland.log
```

## Package Equivalents

Here are the main package mappings from Arch to Fedora:

| Arch Package | Fedora Package | Notes |
|--------------|----------------|-------|
| `ttf-material-symbols-variable-git` | Manual install | Downloaded from Google Fonts |
| `paru` | `dnf` | Native package manager |
| `base-devel` | `Development Tools` | Group install |
| `cliphist` | Built from source | Go application |
| `swww` | Built from source | Rust application |
| `grim` | Built from source | Meson build |
| `slurp` | Built from source | Meson build |
| `hyprutils` | Built from source | Part of Hyprland ecosystem |
| `hypridle` | Built from source | Part of Hyprland ecosystem |
| `hyprlock` | Built from source | Part of Hyprland ecosystem |

## Troubleshooting

### Build Failures
If any package fails to build:
1. Check that all development tools are installed
2. Make sure Rust and Go are properly installed
3. Check for missing dependencies in the build output

### Font Issues
If icons still don't display:
1. Verify fonts are installed: `fc-list | grep -i material`
2. Check font cache: `fc-cache -fv`
3. Restart applications that use the fonts

### Permission Issues
If you get permission errors:
1. Make sure you're not running as root
2. Check sudo privileges
3. Verify file permissions in ~/.local/

## Getting Help

If you encounter issues:
1. Check the HyprLuna Discord: https://discord.gg/qnAHD9keWr
2. Open an issue on the GitHub repository
3. Check Hyprland logs for specific errors

## Contributing

If you successfully install HyprLuna on Fedora and find improvements or fixes, please contribute back to the project by:
1. Opening a pull request with your fixes
2. Documenting any additional issues you encountered
3. Helping other Fedora users in the Discord community

---

**Note**: This installation method is community-contributed and may need updates as the project evolves. Always check the official documentation for the latest information.
