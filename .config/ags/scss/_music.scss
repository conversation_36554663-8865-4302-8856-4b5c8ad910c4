// TODO: need to be updated and fix glass effect problem
@import './wal';
@import './colors';
@import './lib_mixins';

$music_transparentize_amount: 0.5;
$music_gradient2: $color2;
$music_gradient1: mix($layer0, $onLayer3, 60%);
$music_gradient3: $color3;
$music_colorstart_transparentize: 0.9;
$music_extra_transparentize: 0.9;

$secondaryContainer: transparentize(mix(mix($background, $color2, 50%), $color6, 80%), 0.5);

.osd-music {
    @include menu_decel;
    background-color: $background;
    border: 0.2px solid $outliner;
    padding: 1.423rem 1.423rem;
    min-width: 600px;
}
.osd-music-round {
    border-radius: $rounding_large $rounding_large 0 0;
}

.normal-music {
    background-color: $layer0; // coherent theme
}
.amberoled {
    background:
// Central glow effect
    // Solid base layer
    linear-gradient($background, $background);

}
.corner-amberoled {
    background-color: $background;
}

.osd-music-cover {
    @include menu_decel;
    margin: 1rem;
    margin-top: 1rem;
    min-width: 150px;
    min-height: 150px;

    .osd-music-cover-art {
        border-radius: 16px;
        margin: 0.4rem;
    }
}
.elevate-music{
    border-radius: $rounding_large;
}
.osd-music-info {
    margin: 0.5rem 0;
}

.osd-music-title {
    @include element_decel;
    @include titlefont;
    font-size: 2.5rem;
    color: $onSecondaryContainer;
}

.osd-music-artists {
    @include element_decel;
    @include mainfont;
    font-size: 1.5rem;
    color: mix($onSecondaryContainer, $secondaryContainer, 80%);
}

.osd-music-pill {
    @include element_decel;
    @include full-rounding;
    @include titlefont;
    min-width: 2.833rem;
    padding: 0.273rem 0.682rem;
    background-color: $secondaryContainer;
    color: $onSecondaryContainer;
}

.osd-music-controls {
    @include element_decel;
    @include full-rounding;
    @include titlefont;
    min-width: 2.833rem;
    padding: 0.3rem;
    background-color: $secondaryContainer;
    color: $onSecondaryContainer;
    border: 1px solid transparentize($outliner, 0.5);
}

.osd-music-controlbtn {
    @include menu_decel;
    min-width: 2.3rem;
    min-height: 2.3rem;
    border-radius: 1rem;
    margin: 0 0.1rem;
    transition: all 200ms cubic-bezier(0.05, 0.7, 0.1, 1);
    background-color: transparentize($secondaryContainer, 0.7);
}

.osd-music-controlbtn:hover,
.osd-music-controlbtn:focus {
    background-color: mix($secondaryContainer, $primary, 85%);
    margin-top: -1px;
    margin-bottom: 1px;
}

.osd-music-controlbtn:active {
    background-color: mix($secondaryContainer, $primary, 75%);
    margin-top: 0px;
    margin-bottom: 0px;
}

.osd-music-controlbtn.active {
    background-color: mix($secondaryContainer, $primary, 70%);
    color: $onPrimary;
}

.osd-music-player-btn {
    min-width: 6rem;
    padding: 0 0.2rem;
    margin: 0 0.3rem;
    border-radius: 1rem;
    background-color: mix($secondaryContainer, $onSecondaryContainer, 95%);
    border: 1px solid transparentize($outliner, 0.7);
}

.osd-music-player-btn:hover {
    background-color: mix($secondaryContainer, $primary, 90%);
}

.osd-music-player-indicator {
    transition: opacity 200ms cubic-bezier(0.05, 0.7, 0.1, 1);
}

.osd-music-player-name {
    font-size: 0.9rem;
    font-weight: 600;
    color: $onSecondaryContainer;
}

.osd-music-controlbtn-txt {
    @include element_decel;
    transition: 100ms cubic-bezier(0.05, 0.7, 0.1, 1);
    @include icon-material;
    font-size: 1.4rem;
    margin: 0;
}

.osd-music-circprog {
    @include fluent_decel_long;
    min-width: 0.609rem; // width of progress
    min-height: 4.068rem;
    padding: 0.273rem;
    color: $onSecondaryContainer;
}

.osd-music-playstate {
    @include menu_decel;
    min-height: 3.5rem;
    min-width: 3.5rem;
    border-radius: 100px;
    background-color: $secondaryContainer;
    color: $onSecondaryContainer;
}

.osd-music-playstate-btn>label {
    transition: 50ms cubic-bezier(0.05, 0.7, 0.1, 1);
    @include icon-material;
    font-size: 2.364rem;
    margin: -0.1rem 0rem;
}
.cava-container {
    min-height: 140px;
    padding: 5px;
    border-radius: 12px;
    .cava-visualizer {
        font-family: techfont;
        font-size: 24px;
        color: $primary;
        border-radius: 12px;
        background-color: $secondaryContainer;
        .cava-bar {
            background-color: mix($secondary, $background, 70%);
            border-radius: 4px;
            transition: all 80ms cubic-bezier(0.4, 0, 0.2, 1);

            &.cava-bar-low {
                background-color: mix($secondary, $background, 70%);
            }

            &.cava-bar-med {
                background-color: mix($primary, $background, 80%);
            }

            &.cava-bar-high {
                background-color: mix($primary, $background, 95%);
            }
        }
        .cava-bar-up {
            border-radius: 4px 4px 0 0;
        }
        .cava-bar-down {
            border-radius: 0 0 4px 4px;
        }
    }
}

// Mode switching styles
.music-mode-controls {
    @include element_decel;
    padding: 0.5rem;
    margin-bottom: 1rem;
    border-radius: $rounding_medium;
    background-color: transparentize($secondaryContainer, 0.3);
}

.music-mode-button {
    @include menu_decel;
    @include full-rounding;
    min-width: 1.6rem;
    min-height: 1.6rem;
    background-color: $secondaryContainer;
    color: $onSecondaryContainer;

    &:hover {
        background-color: mix($secondaryContainer, $onSecondaryContainer, 90%);
    }

    &:active {
        background-color: mix($secondaryContainer, $onSecondaryContainer, 85%);
    }
}

.music-mode-indicator {
    @include titlefont;
    color: $onSecondaryContainer;
    font-size: 1.2rem;
    margin: 0.4rem 0.1rem;
}

// MPRIS player list styles
.mpris-player-button {
    @include element_decel;
    border-radius: $rounding_medium;
    padding: 0.6rem 0.8rem;
    margin: 0.2rem 0;
    background-color: $secondaryContainer;

    &:hover {
        background-color: mix($secondaryContainer, $onSecondaryContainer, 90%);
    }

    &:active {
        background-color: mix($secondaryContainer, $onSecondaryContainer, 85%);
    }

    &.mpris-player-button-active {
        background-color: mix($primary, $secondaryContainer, 30%);
        border-left: 3px solid $primary;
    }
}

.player-name-label {
    @include titlefont;
    color: $onSecondaryContainer;
    font-size: 1.1rem;
}

.player-track-label {
    @include mainfont;
    color: mix($onSecondaryContainer, $secondaryContainer, 80%);
    font-size: 0.9rem;
}

.empty-music-message {
    padding: 2rem;

    .empty-music-icon {
        color: mix($onSecondaryContainer, $secondaryContainer, 60%);
        margin-bottom: 1rem;
    }

    .empty-music-title {
        @include titlefont;
        color: $onSecondaryContainer;
        font-size: 1.4rem;
        margin-bottom: 0.5rem;
    }

    .empty-music-subtitle {
        @include mainfont;
        color: mix($onSecondaryContainer, $secondaryContainer, 80%);
        font-size: 1rem;
    }
}

.osd-music-volume {
    margin: 5px 0;
    padding: 5px;
    border-radius: 8px;
}

.osd-music-volume-slider {
    margin: 0 5px;

    trough {
        background-color: mix($secondaryContainer, $onSecondaryContainer, 85%);
        border-radius: 100px;
        min-height: 6px;
    }

    highlight {
        background-color: $primary;
        border-radius: 100px;
    }

    slider {
        background-color: $primary;
        border-radius: 50%;
        min-width: 16px;
        min-height: 16px;
        margin: -5px;
    }
}

.osd-music-player-menu {
    background-color: $background;
    border: 1px solid $outliner;
    border-radius: $rounding_medium;
    padding: 4px;

    menuitem {
        padding: 6px 8px;
        border-radius: $rounding_small;

        &:hover {
            background-color: $secondaryContainer;
        }
    }
}
