.cheatsheet-bg {
    @include large-rounding;
    @include elevation-border;
    @include elevation2;
    margin-bottom: 0.682rem;
    background-color: $layer0;
    padding: 1.364rem;
}

.cheatsheet-title {
    color: $cheatsheetTitle;
}

.cheatsheet-bind-lineheight {
    min-height: 2.045rem;
}

.cheatsheet-key {
    @include techfont;
    min-height: 1.364rem;
    min-width: 1.364rem;
    margin: 0.17rem;
    padding: 0.136rem 0.205rem;
    -gtk-outline-radius: 0.409rem;
    color: $cheatsheetTitle;
    border-radius: 0.409rem;
    border: 0.068rem solid $cheatsheetTitle;
    box-shadow: 0rem 0.136rem 0rem $cheatsheetTitle;
}

.cheatsheet-key-notkey {
    min-height: 1.364rem;
    padding: 0.136rem 0.205rem;
    margin: 0.17rem;
    color: $onLayer0;
}

@for $i from 1 through 8 {
    .cheatsheet-color-#{$i} {
        color: nth($cheatsheetColors, $i);
        border-color: nth($cheatsheetColors, $i);
        box-shadow: 0rem 0.136rem 0rem nth($cheatsheetColors, $i);
    }
}

// .cheatsheet-action {}

.cheatsheet-closebtn {
    @include element_decel;
    @include full-rounding;
    min-width: 2.386rem;
    min-height: 2.386rem;
}

.cheatsheet-closebtn:hover,
.cheatsheet-closebtn:focus {
    background-color: $layer0Hover;
}

.cheatsheet-closebtn:active {
    background-color: $layer0Active;
}

.cheatsheet-category-title {
    @include titlefont;
    font-size: 1.705rem;
}

@mixin cheatsheet-periodictable-element {
    min-width: 5.455rem;
    min-height: 5.455rem;
    @include small-rounding;
    background-color: $layer1;
    color: $onLayer1;
}

.cheatsheet-periodictable-elementsymbol {
    @include readingfont;
    font-size: 1.705rem;
    font-weight: bold;
}

.cheatsheet-periodictable-elementnum {
    @include full-rounding;
    min-width: 1.364rem;
    min-height: 1.364rem;
    background-color: $term0;
    color: $onBackground;
}

// Periodic table colors using Material You palette
$colormetal: $primaryContainer;      // Metals - using primary container for main elements
$colornonmetal: $tertiaryContainer;  // Non-metals - using tertiary for contrast
$colornoblegas: $secondaryContainer; // Noble gases - using secondary for distinction
$colorlanthanum: $surfaceContainerHighest; // Lanthanides - using surface container for subtle difference
$coloractinium: $surfaceContainerHigh;     // Actinides - using another surface container level

.cheatsheet-periodictable-empty {
    @include small-rounding;
    min-width: 5.455rem;
    min-height: 5.455rem;
}

.cheatsheet-periodictable-metal {
    @include cheatsheet-periodictable-element;
    background-color: $colormetal;
    color: $onPrimaryContainer;  // Better contrast for text
}

.cheatsheet-periodictable-nonmetal {
    @include cheatsheet-periodictable-element;
    background-color: $colornonmetal;
    color: $onPrimaryContainer;  // Better contrast for text
}

.cheatsheet-periodictable-noblegas {
    @include cheatsheet-periodictable-element;
    background-color: $colornoblegas;
    color: $onPrimaryContainer;  // Better contrast for text
}

.cheatsheet-periodictable-lanthanum {
    @include cheatsheet-periodictable-element;
    background-color: $colorlanthanum;
    color: $onPrimaryContainer;  // Better contrast for text
}

.cheatsheet-periodictable-actinium {
    @include cheatsheet-periodictable-element;
    background-color: $coloractinium;
    color: $onPrimaryContainer;  // Better contrast for text
}

.cheatsheet-periodictable-legend-color-wrapper {
    @include full-rounding;
    padding: 0.273rem;
    border: 0.136rem solid $onLayer0;
}

@mixin cheatsheet-periodictable-legend-color {
    @include full-rounding;
    min-width: 1.023rem;
    min-height: 1.023rem;
}

.cheatsheet-periodictable-legend-color-metal {
    @include cheatsheet-periodictable-legend-color;
    background-color: $colormetal;
}

.cheatsheet-periodictable-legend-color-nonmetal {
    @include cheatsheet-periodictable-legend-color;
    background-color: $colornonmetal;
}

.cheatsheet-periodictable-legend-color-noblegas {
    @include cheatsheet-periodictable-legend-color;
    background-color: $colornoblegas;
}

.cheatsheet-periodictable-legend-color-lanthanum {
    @include cheatsheet-periodictable-legend-color;
    background-color: $colorlanthanum;
}

.cheatsheet-periodictable-legend-color-actinium {
    @include cheatsheet-periodictable-legend-color;
    background-color: $coloractinium;
}
