# HyprLuna for Fedora - Quick Start Guide

Welcome! This repository now includes Fedora support for the HyprLuna dotfiles. Here's how to get started on your Fedora 42 system.

## 🚀 Quick Installation

### Option 1: Automated Installation (Recommended)

Run the Fedora-specific installer that handles all dependencies and builds missing packages:

```bash
# Make the script executable
chmod +x installer-fedora.sh

# Run the Fedora installer
./installer-fedora.sh
```

This will:
- ✅ Install all required packages via DNF
- ✅ Build missing packages from source (cliphist, swww, grim, slurp, etc.)
- ✅ Install Material Symbols fonts properly
- ✅ Build and install AGS v1 with correct dependencies
- ✅ Set up proper PATH variables

### Option 2: Step-by-Step Installation

If you prefer more control or the automated installer fails:

1. **Install dependencies first:**
   ```bash
   ./installer-fedora.sh
   ```

2. **Source your bashrc:**
   ```bash
   source ~/.bashrc
   ```

3. **Run the main installer:**
   ```bash
   ./installer.sh
   ```

## 🔧 Fix Material Icons Issue

If you're experiencing the Material Icons issue (some icons not showing), run the dedicated fix script:

```bash
# Make executable and run
chmod +x fix-material-icons.sh
./fix-material-icons.sh
```

This script specifically addresses the font issues reported by Fedora users.

## 📋 What's Different for Fedora?

The main changes made for Fedora compatibility:

### Package Management
- Uses `dnf` instead of `pacman`/`paru`
- Maps Arch packages to Fedora equivalents
- Builds AUR packages from source

### Font Handling
- Downloads Material Symbols fonts directly from Google
- Creates proper fontconfig configuration
- Installs additional icon fonts via DNF

### Build Dependencies
- Installs Development Tools group
- Sets up Rust and Go toolchains
- Handles glib dependencies for AGS v1

### Key Packages Built from Source
- `cliphist` - Clipboard manager
- `swww` - Wallpaper daemon
- `grim` & `slurp` - Screenshot tools
- `hyprutils`, `hypridle`, `hyprlock` - Hyprland ecosystem
- `wlogout` - Logout menu
- `AGS v1` - Widget system

## 🐛 Known Issues & Solutions

### Material Icons Not Showing
**Problem**: Some Material Symbols icons don't display properly.

**Solution**: Run the fix script:
```bash
./fix-material-icons.sh
```

### AGS Not Starting
**Problem**: AGS command not found or fails to start.

**Solution**: 
```bash
# Check if AGS is installed
which ags

# If not found, add to PATH
echo 'export PATH="/usr/local/bin:$PATH"' >> ~/.bashrc
source ~/.bashrc

# Check dependencies
ldd /usr/local/bin/ags
```

### Build Failures
**Problem**: Some packages fail to build from source.

**Solution**:
1. Make sure all development tools are installed
2. Check error messages for missing dependencies
3. Install missing dependencies with `sudo dnf install <package>-devel`

## 📁 File Structure

```
HyprLuna/
├── installer.sh              # Original installer (now Fedora-aware)
├── installer-fedora.sh       # Fedora-specific pre-installer
├── fix-material-icons.sh     # Material Icons fix script
├── FEDORA_INSTALL_GUIDE.md   # Detailed installation guide
└── README_FEDORA.md          # This file
```

## 🆘 Getting Help

If you encounter issues:

1. **Check the detailed guide**: Read `FEDORA_INSTALL_GUIDE.md` for comprehensive troubleshooting
2. **Join Discord**: https://discord.gg/qnAHD9keWr
3. **Check logs**: 
   - Hyprland: `cat ~/.local/share/hyprland/hyprland.log`
   - AGS: Check terminal output when running `ags`
4. **Verify installation**:
   ```bash
   # Check key components
   which ags swww cliphist grim slurp
   fc-list | grep -i material
   ```

## 🤝 Contributing

Found a fix or improvement? Please contribute back:
1. Test your changes thoroughly
2. Update documentation
3. Submit a pull request
4. Help other Fedora users in Discord

## ⚠️ Important Notes

- **Backup your configs**: The installer will backup existing configurations
- **Restart required**: Log out and back in after installation
- **Terminal restart**: Run `source ~/.bashrc` or restart terminal after installation
- **Font cache**: May need to refresh with `fc-cache -fv`

## 🎉 Success!

Once installed successfully:
1. Log out of your current session
2. Log back in selecting "Hyprland" as your session
3. Enjoy your beautiful HyprLuna desktop!

---

**Made with ❤️ for the Fedora community**

*This Fedora adaptation addresses the main compatibility issues while maintaining the full HyprLuna experience.*
