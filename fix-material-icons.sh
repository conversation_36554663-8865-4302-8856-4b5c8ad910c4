#!/bin/bash
# Fix Material Icons for HyprLuna on Fedora
# This script specifically addresses the Material Symbols font issues

set -e

print_info() {
    echo -e "\e[32mINFO:\e[0m $1"
}

print_warning() {
    echo -e "\e[33mWARNING:\e[0m $1"
}

print_error() {
    echo -e "\e[31mERROR:\e[0m $1" >&2
}

print_step() {
    echo -e "\n\e[34m-->\e[0m \e[1m$1\e[0m"
}

print_step "Fixing Material Icons for HyprLuna on Fedora"

# Create fonts directory
mkdir -p ~/.local/share/fonts

print_info "Downloading Material Symbols fonts..."

# Download all variants of Material Symbols fonts
fonts=(
    "https://github.com/google/material-design-icons/raw/master/variablefont/MaterialSymbolsOutlined%5BFILL%2CGRAD%2Copsz%2Cwght%5D.ttf"
    "https://github.com/google/material-design-icons/raw/master/variablefont/MaterialSymbolsRounded%5BFILL%2CGRAD%2Copsz%2Cwght%5D.ttf"
    "https://github.com/google/material-design-icons/raw/master/variablefont/MaterialSymbolsSharp%5BFILL%2CGRAD%2Copsz%2Cwght%5D.ttf"
)

font_names=(
    "MaterialSymbolsOutlined.ttf"
    "MaterialSymbolsRounded.ttf"
    "MaterialSymbolsSharp.ttf"
)

for i in "${!fonts[@]}"; do
    font_url="${fonts[$i]}"
    font_name="${font_names[$i]}"
    
    print_info "Downloading $font_name..."
    if curl -L "$font_url" -o ~/.local/share/fonts/"$font_name"; then
        print_info "Successfully downloaded $font_name"
    else
        print_warning "Failed to download $font_name"
    fi
done

# Also try to get the fonts from Google Fonts API
print_info "Attempting to download from Google Fonts API..."

# Download Material Icons (the older version that might be more compatible)
if curl -L "https://fonts.googleapis.com/icon?family=Material+Icons" -o /tmp/material-icons.css; then
    # Extract font URLs from CSS and download them
    font_urls=$(grep -o 'https://fonts.gstatic.com/s/materialicons/[^)]*' /tmp/material-icons.css | head -5)
    
    for url in $font_urls; do
        filename=$(basename "$url" | sed 's/[?&].*//')
        if [[ "$filename" == *".woff2" ]] || [[ "$filename" == *".ttf" ]]; then
            print_info "Downloading font from: $url"
            curl -L "$url" -o ~/.local/share/fonts/"material-icons-$filename" || print_warning "Failed to download $filename"
        fi
    done
    
    rm -f /tmp/material-icons.css
fi

# Install additional icon fonts that might be needed
print_info "Installing additional icon fonts via DNF..."
sudo dnf install -y google-material-design-icons-fonts \
                    fontawesome-fonts \
                    fontawesome-fonts-web \
                    google-noto-fonts \
                    google-noto-emoji-fonts || {
    print_warning "Some icon fonts failed to install via DNF"
}

# Create a fontconfig configuration to prioritize Material Symbols
print_info "Creating fontconfig configuration..."
mkdir -p ~/.config/fontconfig/conf.d

cat > ~/.config/fontconfig/conf.d/99-material-symbols.conf << 'EOF'
<?xml version="1.0"?>
<!DOCTYPE fontconfig SYSTEM "fonts.dtd">
<fontconfig>
  <!-- Material Symbols font configuration -->
  <alias>
    <family>Material Symbols Outlined</family>
    <prefer>
      <family>Material Symbols Outlined</family>
    </prefer>
  </alias>
  
  <alias>
    <family>Material Symbols Rounded</family>
    <prefer>
      <family>Material Symbols Rounded</family>
    </prefer>
  </alias>
  
  <alias>
    <family>Material Symbols Sharp</family>
    <prefer>
      <family>Material Symbols Sharp</family>
    </prefer>
  </alias>
  
  <!-- Fallback for Material Icons -->
  <alias>
    <family>Material Icons</family>
    <prefer>
      <family>Material Symbols Outlined</family>
      <family>Material Symbols Rounded</family>
      <family>FontAwesome</family>
    </prefer>
  </alias>
</fontconfig>
EOF

print_info "Refreshing font cache..."
fc-cache -fv

print_step "Verification"

print_info "Checking installed Material fonts..."
if fc-list | grep -i "material"; then
    print_info "✓ Material fonts found in system"
else
    print_warning "✗ No Material fonts found - there may be an issue"
fi

print_info "Checking font cache..."
if fc-list : family | grep -i "material"; then
    print_info "✓ Material fonts available in font cache"
else
    print_warning "✗ Material fonts not found in font cache"
fi

print_step "Testing Icon Display"

print_info "Testing if icons can be displayed..."

# Test with a simple icon display
if command -v echo &>/dev/null; then
    print_info "Testing Material Icons characters:"
    echo "Home icon: 🏠"
    echo "Settings icon: ⚙️"
    echo "Search icon: 🔍"
    
    # Test with Material Symbols Unicode points
    printf "Material Symbols test: \ue88a \ue8b8 \ue8cc\n"
fi

print_step "Additional Fixes"

# Fix for AGS specifically
if [ -d ~/.config/ags ]; then
    print_info "Applying AGS-specific font fixes..."
    
    # Create a CSS override for AGS
    mkdir -p ~/.config/ags/scss
    cat > ~/.config/ags/scss/_material_icons_fix.scss << 'EOF'
/* Material Icons Fix for Fedora */
.material-symbols-outlined,
.material-icons {
    font-family: "Material Symbols Outlined", "Material Icons", "FontAwesome", sans-serif;
    font-weight: normal;
    font-style: normal;
    font-size: 24px;
    line-height: 1;
    letter-spacing: normal;
    text-transform: none;
    display: inline-block;
    white-space: nowrap;
    word-wrap: normal;
    direction: ltr;
    -webkit-font-feature-settings: 'liga';
    -webkit-font-smoothing: antialiased;
}
EOF
    
    print_info "AGS font fix applied. You may need to restart AGS."
fi

print_step "Completion"

print_info "Material Icons fix completed!"
print_info ""
print_info "What was done:"
print_info "• Downloaded Material Symbols fonts from Google"
print_info "• Installed additional icon fonts via DNF"
print_info "• Created fontconfig configuration"
print_info "• Refreshed font cache"
print_info "• Applied AGS-specific fixes (if AGS config found)"
print_info ""
print_info "Next steps:"
print_info "1. Restart any applications using Material Icons"
print_info "2. If using AGS, restart it: pkill ags && ags"
print_info "3. Log out and log back in if issues persist"
print_info ""
print_info "If icons still don't show properly:"
print_info "• Check the HyprLuna Discord for help: https://discord.gg/qnAHD9keWr"
print_info "• Run this script again"
print_info "• Check application-specific font settings"

print_warning "Note: Some applications may need to be restarted to pick up the new fonts."
