#!/bin/bash
# HyprLuna Fedora Installer
# Adapted for Fedora/Nobara systems
# Exit immediately if a command exits with a non-zero status.
set -e

# --- Configuration ---
REPO_URL="https://github.com/Lunaris-Project/HyprLuna.git"
AGSV1_REPO_URL="https://github.com/Lunaris-Project/agsv1"
HYPRLUNA_DIR="$HOME/HyprLuna"
BACKUP_DIR="$HOME/HyprLuna-User-Bak"
BUILD_TEMP_DIR=$(mktemp -d)

# Fedora packages that can be installed via DNF
FEDORA_PACKAGES=(
    git hyprland axel bc coreutils cmake curl rsync wget ripgrep jq npm meson typescript gjs xdg-user-dirs
    brightnessctl ddcutil pavucontrol wireplumber libdbusmenu-gtk3-devel kitty playerctl 
    gobject-introspection-devel glib2-devel gvfs glib2 glibc gtk3-devel libpulse-devel pam-devel 
    gnome-bluetooth gammastep libsoup3-devel libnotify-devel NetworkManager power-profiles-daemon
    upower qt5ct qt5-qtwayland fontconfig jetbrains-mono-fonts-all fish foot starship 
    polkit-gnome gnome-keyring gnome-control-center blueberry webp-pixbuf-loader 
    gtksourceview3-devel yad xdg-user-dirs-gtk tinyxml2-devel gtkmm30-devel 
    gtksourceviewmm3-devel cairomm-devel xdg-desktop-portal xdg-desktop-portal-gtk 
    python3-libsass python3-build python3-pillow python3-setuptools-scm python3-wheel
    tesseract tesseract-langpack-eng python3-psutil wl-clipboard noto-fonts-cjk-vf 
    noto-fonts-emoji cava golang mpv google-noto-fonts google-noto-emoji-fonts 
    google-noto-sans-cjk-fonts fira-code-fonts gtk-layer-shell-devel
)

# Directories that will be copied from the HyprLuna repo to your home directory
DOTFILES_DIRS=(".config" ".local" ".cache" ".vscode" ".fonts" ".ags" "Pictures")

# --- Functions ---
print_step() {
    echo -e "\n\e[34m-->\e[0m \e[1m$1\e[0m"
}

print_info() {
    echo -e "\e[32mINFO:\e[0m $1"
}

print_warning() {
    echo -e "\e[33mWARNING:\e[0m $1"
}

print_error() {
    echo -e "\e[31mERROR:\e[0m $1" >&2
}

# Function to install Material Symbols fonts
install_material_fonts() {
    print_info "Installing Material Symbols fonts..."
    
    # Create fonts directory
    mkdir -p ~/.local/share/fonts
    
    # Download and install Material Symbols fonts
    if ! fc-list | grep -q "Material Symbols"; then
        print_info "Downloading Material Symbols fonts from Google Fonts..."
        
        # Download the fonts
        curl -L "https://fonts.google.com/download?family=Material%20Symbols%20Outlined" \
             -o /tmp/material-symbols-outlined.zip || {
            print_warning "Failed to download Material Symbols Outlined font, trying alternative..."
            # Alternative download method
            wget -O /tmp/MaterialSymbolsOutlined.ttf \
                "https://github.com/google/material-design-icons/raw/master/variablefont/MaterialSymbolsOutlined%5BFILL%2CGRAD%2Copsz%2Cwght%5D.ttf" || {
                print_warning "Failed to download Material Symbols font"
                return 1
            }
            cp /tmp/MaterialSymbolsOutlined.ttf ~/.local/share/fonts/
        }
        
        # If zip download succeeded, extract it
        if [ -f /tmp/material-symbols-outlined.zip ]; then
            cd /tmp
            unzip -q material-symbols-outlined.zip -d material-symbols/
            find material-symbols/ -name "*.ttf" -exec cp {} ~/.local/share/fonts/ \;
            rm -rf material-symbols/ material-symbols-outlined.zip
        fi
        
        # Refresh font cache
        fc-cache -fv
        print_info "Material Symbols fonts installed successfully."
    else
        print_info "Material Symbols fonts already installed."
    fi
}

# Function to build packages from source
build_from_source() {
    local build_dir="$BUILD_TEMP_DIR"
    cd "$build_dir"
    
    # Install cliphist
    if ! command -v cliphist &>/dev/null; then
        print_info "Building cliphist from source..."
        git clone https://github.com/sentriz/cliphist.git
        cd cliphist
        go build .
        mkdir -p ~/.local/bin
        cp cliphist ~/.local/bin/ || print_warning "Failed to install cliphist"
        cd "$build_dir"
    fi
    
    # Install swww
    if ! command -v swww &>/dev/null; then
        print_info "Building swww from source..."
        git clone https://github.com/Horus645/swww.git
        cd swww
        cargo build --release
        mkdir -p ~/.local/bin
        cp target/release/swww ~/.local/bin/ || print_warning "Failed to install swww"
        cp target/release/swww-daemon ~/.local/bin/ || print_warning "Failed to install swww-daemon"
        cd "$build_dir"
    fi
    
    # Install grim and slurp (screenshot tools)
    if ! command -v grim &>/dev/null; then
        print_info "Building grim from source..."
        git clone https://github.com/emersion/grim.git
        cd grim
        meson build
        ninja -C build
        sudo ninja -C build install || print_warning "Failed to build grim"
        cd "$build_dir"
    fi
    
    if ! command -v slurp &>/dev/null; then
        print_info "Building slurp from source..."
        git clone https://github.com/emersion/slurp.git
        cd slurp
        meson build
        ninja -C build
        sudo ninja -C build install || print_warning "Failed to build slurp"
        cd "$build_dir"
    fi
    
    # Install wf-recorder
    if ! command -v wf-recorder &>/dev/null; then
        print_info "Building wf-recorder from source..."
        git clone https://github.com/ammen99/wf-recorder.git
        cd wf-recorder
        meson build --prefix=/usr/local
        ninja -C build
        sudo ninja -C build install || print_warning "Failed to build wf-recorder"
        cd "$build_dir"
    fi
    
    # Install swappy
    if ! command -v swappy &>/dev/null; then
        print_info "Building swappy from source..."
        git clone https://github.com/jtheoof/swappy.git
        cd swappy
        meson build --prefix=/usr/local
        ninja -C build
        sudo ninja -C build install || print_warning "Failed to build swappy"
        cd "$build_dir"
    fi

    # Install hyprutils
    if ! pkg-config --exists hyprutils; then
        print_info "Building hyprutils from source..."
        git clone https://github.com/hyprwm/hyprutils.git
        cd hyprutils
        cmake -DCMAKE_INSTALL_PREFIX=/usr/local -B build
        cmake --build build -j$(nproc)
        sudo cmake --install build || print_warning "Failed to build hyprutils"
        cd "$build_dir"
    fi

    # Install hypridle
    if ! command -v hypridle &>/dev/null; then
        print_info "Building hypridle from source..."
        git clone https://github.com/hyprwm/hypridle.git
        cd hypridle
        cmake -DCMAKE_INSTALL_PREFIX=/usr/local -B build
        cmake --build build -j$(nproc)
        sudo cmake --install build || print_warning "Failed to build hypridle"
        cd "$build_dir"
    fi

    # Install hyprlock
    if ! command -v hyprlock &>/dev/null; then
        print_info "Building hyprlock from source..."
        git clone https://github.com/hyprwm/hyprlock.git
        cd hyprlock
        cmake -DCMAKE_INSTALL_PREFIX=/usr/local -B build
        cmake --build build -j$(nproc)
        sudo cmake --install build || print_warning "Failed to build hyprlock"
        cd "$build_dir"
    fi

    # Install wlogout
    if ! command -v wlogout &>/dev/null; then
        print_info "Building wlogout from source..."
        git clone https://github.com/ArtsyMacaw/wlogout.git
        cd wlogout
        meson build --prefix=/usr/local
        ninja -C build
        sudo ninja -C build install || print_warning "Failed to build wlogout"
        cd "$build_dir"
    fi

    # Install hyprpicker
    if ! command -v hyprpicker &>/dev/null; then
        print_info "Building hyprpicker from source..."
        git clone https://github.com/hyprwm/hyprpicker.git
        cd hyprpicker
        cmake -DCMAKE_INSTALL_PREFIX=/usr/local -B build
        cmake --build build -j$(nproc)
        sudo cmake --install build || print_warning "Failed to build hyprpicker"
        cd "$build_dir"
    fi
}

# --- Script Start ---
print_step "Starting HyprLuna Installation Script for Fedora"

# Check if running as root
if [ "$EUID" -eq 0 ]; then
    print_error "This script should be run as a normal user, not root."
    print_error "The script will use 'sudo' internally where needed."
    exit 1
fi

# Check if on Fedora
DISTRO_ID=$(grep "^ID=" /etc/os-release | cut -d'=' -f2 | tr -d '"')
if [[ "$DISTRO_ID" != "fedora" && "$DISTRO_ID" != "nobara" ]]; then
    print_warning "This script is designed for Fedora/Nobara systems."
    print_warning "Detected: $DISTRO_ID - It may not work correctly on other systems."
    read -p "Do you want to continue anyway? (y/n): " consent < /dev/tty
    if [ "$consent" != "y" ]; then
        print_info "Installation aborted by user."
        exit 1
    fi
fi

# Clean up temporary directories on script exit
trap 'rm -rf "$BUILD_TEMP_DIR"' EXIT

# Check if git is installed
if ! command -v git &>/dev/null; then
    print_error "Git is required but not installed."
    print_error "Please install git first using: sudo dnf install git"
    exit 1
fi

print_step "Step 1: Installing Development Tools and Repositories"

print_info "Installing development tools and RPM Fusion repositories..."

# Install development tools
sudo dnf groupinstall -y "Development Tools" "C Development Tools and Libraries" || {
    print_warning "Failed to install some development tools, continuing..."
}

# Enable RPM Fusion repositories
sudo dnf install -y https://mirrors.rpmfusion.org/free/fedora/rpmfusion-free-release-$(rpm -E %fedora).noarch.rpm \
                    https://mirrors.rpmfusion.org/nonfree/fedora/rpmfusion-nonfree-release-$(rpm -E %fedora).noarch.rpm || {
    print_warning "Failed to install RPM Fusion repositories, some packages may not be available."
}

# Install Rust if not present
if ! command -v cargo &>/dev/null; then
    print_info "Installing Rust toolchain..."
    curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y
    source ~/.cargo/env
fi

# Install Go if not present
if ! command -v go &>/dev/null; then
    print_info "Installing Go..."
    sudo dnf install -y golang || print_warning "Failed to install Go"
fi

print_step "Step 2: Installing Required Packages"

print_info "Installing packages via DNF..."
sudo dnf install -y "${FEDORA_PACKAGES[@]}" || {
    print_warning "Some packages failed to install via DNF. This is expected for some packages."
}

print_step "Step 3: Installing Fonts"
install_material_fonts

print_step "Step 4: Building Additional Packages from Source"
build_from_source

print_step "Step 5: Installing AGS v1"

# Check if ags is already installed
if command -v ags &>/dev/null; then
    print_info "AGS seems to be installed. Skipping AGS v1 build."
else
    print_info "Installing typescript globally via npm..."
    sudo npm i -g typescript || {
        print_error "Failed to install typescript globally."
        exit 1
    }

    print_info "Cloning agsv1 repository..."
    git clone --recursive "$AGSV1_REPO_URL" "$BUILD_TEMP_DIR/agsv1" || {
        print_error "Failed to clone agsv1 repository."
        exit 1
    }

    print_info "Building and installing agsv1..."
    cd "$BUILD_TEMP_DIR/agsv1" || {
        print_error "Failed to change directory to agsv1 temp dir."
        exit 1
    }

    # Install additional dependencies for AGS v1 on Fedora
    print_info "Installing additional dependencies for AGS v1 on Fedora..."
    sudo dnf install -y gtk3-devel gtk-layer-shell-devel libpulse-devel \
                        gobject-introspection-devel gjs-devel || {
        print_warning "Some AGS dependencies failed to install"
    }

    # Build AGS v1
    meson build --prefix=/usr/local
    ninja -C build
    sudo ninja -C build install || {
        print_error "Failed to build and install agsv1. Please check the output above."
        exit 1
    }

    cd - >/dev/null

    print_info "AGS v1 installed successfully."
fi

# Add ~/.local/bin and /usr/local/bin to PATH if not already there
if ! echo "$PATH" | grep -q "$HOME/.local/bin"; then
    echo 'export PATH="$HOME/.local/bin:$PATH"' >> ~/.bashrc
fi

if ! echo "$PATH" | grep -q "/usr/local/bin"; then
    echo 'export PATH="/usr/local/bin:$PATH"' >> ~/.bashrc
fi

# Update current session PATH
export PATH="$HOME/.local/bin:/usr/local/bin:$PATH"

print_step "Installation Complete!"
print_info "All packages and dependencies have been installed successfully."
print_warning "Please restart your terminal or run 'source ~/.bashrc' to update your PATH."
print_info ""
print_info "Next steps:"
print_info "1. Restart your terminal or run: source ~/.bashrc"
print_info "2. Run the main HyprLuna installer: ./installer.sh"
print_info "3. Log out and log back in with Hyprland session"
print_info ""
print_info "If you encounter any issues with Material Icons:"
print_info "- Run: fc-cache -fv"
print_info "- Check fonts: fc-list | grep -i material"
print_info ""
print_info "For support, join the HyprLuna Discord: https://discord.gg/qnAHD9keWr"
