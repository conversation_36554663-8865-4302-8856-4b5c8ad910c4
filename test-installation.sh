#!/bin/bash
# Test HyprLuna Installation on Fedora
# This script verifies that all components are properly installed

print_info() {
    echo -e "\e[32m✓\e[0m $1"
}

print_warning() {
    echo -e "\e[33m⚠\e[0m $1"
}

print_error() {
    echo -e "\e[31m✗\e[0m $1"
}

print_step() {
    echo -e "\n\e[34m==>\e[0m \e[1m$1\e[0m"
}

print_step "Testing HyprLuna Installation"

# Test basic commands
commands=(
    "git"
    "hyprland"
    "ags"
    "swww"
    "cliphist"
    "grim"
    "slurp"
    "wf-recorder"
    "swappy"
    "hypridle"
    "hyprlock"
    "wlogout"
    "hyprpicker"
    "kitty"
    "foot"
    "rofi"
    "pavucontrol"
    "playerctl"
)

print_step "Checking Required Commands"
missing_commands=()

for cmd in "${commands[@]}"; do
    if command -v "$cmd" &>/dev/null; then
        print_info "$cmd is installed"
    else
        print_error "$cmd is missing"
        missing_commands+=("$cmd")
    fi
done

# Test fonts
print_step "Checking Fonts"

if fc-list | grep -qi "material"; then
    print_info "Material fonts are installed"
    fc-list | grep -i "material" | head -3
else
    print_error "Material fonts are missing"
fi

if fc-list | grep -qi "jetbrains"; then
    print_info "JetBrains Mono font is installed"
else
    print_warning "JetBrains Mono font might be missing"
fi

if fc-list | grep -qi "noto"; then
    print_info "Noto fonts are installed"
else
    print_warning "Noto fonts might be missing"
fi

# Test AGS specifically
print_step "Testing AGS"

if command -v ags &>/dev/null; then
    print_info "AGS command is available"
    
    # Test AGS version
    if ags --version &>/dev/null; then
        ags_version=$(ags --version 2>/dev/null || echo "unknown")
        print_info "AGS version: $ags_version"
    else
        print_warning "AGS version check failed"
    fi
    
    # Test AGS dependencies
    if ldd "$(which ags)" &>/dev/null; then
        print_info "AGS dependencies are satisfied"
    else
        print_warning "AGS dependency check failed"
    fi
else
    print_error "AGS is not installed or not in PATH"
fi

# Test Hyprland
print_step "Testing Hyprland"

if command -v hyprland &>/dev/null; then
    print_info "Hyprland is installed"
    
    # Check if we're running in Hyprland
    if [ "$XDG_CURRENT_DESKTOP" = "Hyprland" ]; then
        print_info "Currently running in Hyprland session"
        
        # Test hyprctl
        if command -v hyprctl &>/dev/null; then
            print_info "hyprctl is available"
            if hyprctl version &>/dev/null; then
                hypr_version=$(hyprctl version | head -1)
                print_info "Hyprland version: $hypr_version"
            fi
        fi
    else
        print_warning "Not currently running in Hyprland session"
    fi
else
    print_error "Hyprland is not installed"
fi

# Test configuration files
print_step "Checking Configuration Files"

config_dirs=(
    "$HOME/.config/hypr"
    "$HOME/.config/ags"
    "$HOME/.config/rofi"
    "$HOME/.config/kitty"
    "$HOME/.config/foot"
)

for dir in "${config_dirs[@]}"; do
    if [ -d "$dir" ]; then
        print_info "Configuration directory exists: $dir"
    else
        print_warning "Configuration directory missing: $dir"
    fi
done

# Test important config files
config_files=(
    "$HOME/.config/hypr/hyprland.conf"
    "$HOME/.config/ags/config.js"
)

for file in "${config_files[@]}"; do
    if [ -f "$file" ]; then
        print_info "Configuration file exists: $(basename "$file")"
    else
        print_warning "Configuration file missing: $(basename "$file")"
    fi
done

# Test services
print_step "Testing Services"

# Test if systemd user services are available
if systemctl --user list-unit-files | grep -q "hypridle"; then
    print_info "hypridle service is available"
else
    print_warning "hypridle service not found"
fi

# Summary
print_step "Installation Summary"

if [ ${#missing_commands[@]} -eq 0 ]; then
    print_info "All required commands are installed!"
else
    print_error "Missing commands: ${missing_commands[*]}"
    echo "You may need to run the installer again or install these manually."
fi

# Font cache test
print_step "Font Cache Test"
if fc-cache -fv &>/dev/null; then
    print_info "Font cache refresh successful"
else
    print_warning "Font cache refresh failed"
fi

# Final recommendations
print_step "Recommendations"

if [ ${#missing_commands[@]} -gt 0 ]; then
    echo "❌ Installation appears incomplete. Missing: ${missing_commands[*]}"
    echo "   Run: ./installer-fedora.sh"
elif ! fc-list | grep -qi "material"; then
    echo "⚠️  Material fonts missing. Run: ./fix-material-icons.sh"
elif [ "$XDG_CURRENT_DESKTOP" != "Hyprland" ]; then
    echo "✅ Installation looks good! Log out and log back in with Hyprland session."
else
    echo "🎉 Everything looks great! HyprLuna should be working properly."
fi

echo ""
echo "For help and support:"
echo "• Discord: https://discord.gg/qnAHD9keWr"
echo "• Check logs: cat ~/.local/share/hyprland/hyprland.log"
echo "• Restart AGS: pkill ags && ags"
