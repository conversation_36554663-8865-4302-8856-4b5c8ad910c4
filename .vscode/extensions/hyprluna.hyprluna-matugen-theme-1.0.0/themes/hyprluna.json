{"name": "HyprLuna", "type": "dark", "semanticHighlighting": true, "semanticTokenColors": {"enumMember": {"foreground": "#ffb4a1"}, "variable.constant": {"foreground": "#dac58d"}, "variable.defaultLibrary": {"foreground": "#e7bdb3"}}, "tokenColors": [{"name": "unison punctuation", "scope": "punctuation.definition.delayed.unison,punctuation.definition.list.begin.unison,punctuation.definition.list.end.unison,punctuation.definition.ability.begin.unison,punctuation.definition.ability.end.unison,punctuation.operator.assignment.as.unison,punctuation.separator.pipe.unison,punctuation.separator.delimiter.unison,punctuation.definition.hash.unison", "settings": {"foreground": "#ffb4ab"}}, {"name": "haskell variable generic-type", "scope": "variable.other.generic-type.haskell", "settings": {"foreground": "#ffb4a1"}}, {"name": "haskell storage type", "scope": "storage.type.haskell", "settings": {"foreground": "#dac58d"}}, {"name": "support.variable.magic.python", "scope": "support.variable.magic.python", "settings": {"foreground": "#ffb4ab"}}, {"name": "punctuation.separator.parameters.python", "scope": "punctuation.separator.period.python,punctuation.separator.element.python,punctuation.parenthesis.begin.python,punctuation.parenthesis.end.python", "settings": {"foreground": "#f1dfda"}}, {"name": "variable.parameter.function.language.special.self.python", "scope": "variable.parameter.function.language.special.self.python", "settings": {"foreground": "#e7bdb3"}}, {"name": "storage.modifier.lifetime.rust", "scope": "storage.modifier.lifetime.rust", "settings": {"foreground": "#f1dfda"}}, {"name": "support.function.std.rust", "scope": "support.function.std.rust", "settings": {"foreground": "#ffb4a1"}}, {"name": "entity.name.lifetime.rust", "scope": "entity.name.lifetime.rust", "settings": {"foreground": "#e7bdb3"}}, {"name": "variable.language.rust", "scope": "variable.language.rust", "settings": {"foreground": "#ffb4ab"}}, {"name": "support.constant.edge", "scope": "support.constant.edge", "settings": {"foreground": "#ffb4a1"}}, {"name": "regexp constant character-class", "scope": "constant.other.character-class.regexp", "settings": {"foreground": "#ffb4ab"}}, {"name": "regexp operator.quantifier", "scope": "keyword.operator.quantifier.regexp", "settings": {"foreground": "#dac58d"}}, {"name": "punctuation.definition", "scope": "punctuation.definition.string.begin,punctuation.definition.string.end", "settings": {"foreground": "#e7bdb3"}}, {"name": "Text", "scope": "variable.parameter.function", "settings": {"foreground": "#f1dfda"}}, {"name": "Comment Markup Link", "scope": "comment markup.link", "settings": {"foreground": "#a6928d"}}, {"name": "markup diff", "scope": "markup.changed.diff", "settings": {"foreground": "#e7bdb3"}}, {"name": "diff", "scope": "meta.diff.header.from-file,meta.diff.header.to-file,punctuation.definition.from-file.diff,punctuation.definition.to-file.diff", "settings": {"foreground": "#ffb4a1"}}, {"name": "inserted.diff", "scope": "markup.inserted.diff", "settings": {"foreground": "#e7bdb3"}}, {"name": "deleted.diff", "scope": "markup.deleted.diff", "settings": {"foreground": "#ffb4ab"}}, {"name": "c++ function", "scope": "meta.function.c,meta.function.cpp", "settings": {"foreground": "#ffb4ab"}}, {"name": "c++ block", "scope": "punctuation.section.block.begin.bracket.curly.cpp,punctuation.section.block.end.bracket.curly.cpp,punctuation.terminator.statement.c,punctuation.section.block.begin.bracket.curly.c,punctuation.section.block.end.bracket.curly.c,punctuation.section.parens.begin.bracket.round.c,punctuation.section.parens.end.bracket.round.c,punctuation.section.parameters.begin.bracket.round.c,punctuation.section.parameters.end.bracket.round.c", "settings": {"foreground": "#f1dfda"}}, {"name": "js/ts punctuation separator key-value", "scope": "punctuation.separator.key-value", "settings": {"foreground": "#f1dfda"}}, {"name": "js/ts import keyword", "scope": "keyword.operator.expression.import", "settings": {"foreground": "#ffb4a1"}}, {"name": "math js/ts", "scope": "support.constant.math", "settings": {"foreground": "#e7bdb3"}}, {"name": "math property js/ts", "scope": "support.constant.property.math", "settings": {"foreground": "#dac58d"}}, {"name": "js/ts variable.other.constant", "scope": "variable.other.constant", "settings": {"foreground": "#e7bdb3"}}, {"name": "java type", "scope": ["storage.type.annotation.java", "storage.type.object.array.java"], "settings": {"foreground": "#e7bdb3"}}, {"name": "java source", "scope": "source.java", "settings": {"foreground": "#ffb4ab"}}, {"name": "java modifier.import", "scope": "punctuation.section.block.begin.java,punctuation.section.block.end.java,punctuation.definition.method-parameters.begin.java,punctuation.definition.method-parameters.end.java,meta.method.identifier.java,punctuation.section.method.begin.java,punctuation.section.method.end.java,punctuation.terminator.java,punctuation.section.class.begin.java,punctuation.section.class.end.java,punctuation.section.inner-class.begin.java,punctuation.section.inner-class.end.java,meta.method-call.java,punctuation.section.class.begin.bracket.curly.java,punctuation.section.class.end.bracket.curly.java,punctuation.section.method.begin.bracket.curly.java,punctuation.section.method.end.bracket.curly.java,punctuation.separator.period.java,punctuation.bracket.angle.java,punctuation.definition.annotation.java,meta.method.body.java", "settings": {"foreground": "#f1dfda"}}, {"name": "java modifier.import", "scope": "meta.method.java", "settings": {"foreground": "#ffb4a1"}}, {"name": "java modifier.import", "scope": "storage.modifier.import.java,storage.type.java,storage.type.generic.java", "settings": {"foreground": "#e7bdb3"}}, {"name": "java instanceof", "scope": "keyword.operator.instanceof.java", "settings": {"foreground": "#ffb4a1"}}, {"name": "java variable.name", "scope": "meta.definition.variable.name.java", "settings": {"foreground": "#ffb4ab"}}, {"name": "operator logical", "scope": "keyword.operator.logical", "settings": {"foreground": "#ffb4a1"}}, {"name": "operator bitwise", "scope": "keyword.operator.bitwise", "settings": {"foreground": "#ffb4a1"}}, {"name": "operator channel", "scope": "keyword.operator.channel", "settings": {"foreground": "#ffb4a1"}}, {"name": "support.constant.property-value.scss", "scope": "support.constant.property-value.scss,support.constant.property-value.css", "settings": {"foreground": "#dac58d"}}, {"name": "CSS/SCSS/LESS Operators", "scope": "keyword.operator.css,keyword.operator.scss,keyword.operator.less", "settings": {"foreground": "#ffb4a1"}}, {"name": "keyword.operator", "scope": "keyword.operator.arithmetic,keyword.operator.comparison,keyword.operator.decrement,keyword.operator.increment,keyword.operator.relational", "settings": {"foreground": "#ffb4a1"}}, {"name": "C operator assignment", "scope": "keyword.operator.assignment.c,keyword.operator.comparison.c,keyword.operator.c,keyword.operator.increment.c,keyword.operator.decrement.c,keyword.operator.bitwise.shift.c,keyword.operator.assignment.cpp,keyword.operator.comparison.cpp,keyword.operator.cpp,keyword.operator.increment.cpp,keyword.operator.decrement.cpp,keyword.operator.bitwise.shift.cpp", "settings": {"foreground": "#ffb4a1"}}, {"name": "Punctuation", "scope": "punctuation.separator.delimiter", "settings": {"foreground": "#f1dfda"}}, {"name": "Other punctuation .c", "scope": "punctuation.separator.c,punctuation.separator.cpp", "settings": {"foreground": "#ffb4a1"}}, {"name": "C type posix-reserved", "scope": "support.type.posix-reserved.c,support.type.posix-reserved.cpp", "settings": {"foreground": "#ffb4a1"}}, {"name": "keyword.operator.sizeof.c", "scope": "keyword.operator.sizeof.c,keyword.operator.sizeof.cpp", "settings": {"foreground": "#ffb4a1"}}, {"name": "python parameter", "scope": "variable.parameter.function.language.python", "settings": {"foreground": "#dac58d"}}, {"name": "python type", "scope": "support.type.python", "settings": {"foreground": "#ffb4a1"}}, {"name": "python logical", "scope": "keyword.operator.logical.python", "settings": {"foreground": "#ffb4a1"}}, {"name": "Meta tag", "scope": "meta.tag", "settings": {"foreground": "#f1dfda"}}, {"name": "Strings", "scope": "string", "settings": {"foreground": "#e7bdb3"}}, {"name": "Inherited Class", "scope": "entity.other.inherited-class", "settings": {"foreground": "#e7bdb3"}}, {"name": "Constant other symbol", "scope": "constant.other.symbol", "settings": {"foreground": "#ffb4a1"}}, {"name": "Integers", "scope": "constant.numeric", "settings": {"foreground": "#dac58d"}}, {"name": "Constants", "scope": "constant", "settings": {"foreground": "#dac58d"}}, {"name": "Constants", "scope": "punctuation.definition.constant", "settings": {"foreground": "#dac58d"}}, {"name": "Tags", "scope": "entity.name.tag", "settings": {"foreground": "#ffb4ab"}}, {"name": "Attributes", "scope": "entity.other.attribute-name", "settings": {"foreground": "#dac58d"}}, {"name": "Attribute IDs", "scope": "entity.other.attribute-name.id", "settings": {"fontStyle": "normal", "foreground": "#ffb4a1"}}, {"name": "Attribute class", "scope": "entity.other.attribute-name.class.css", "settings": {"fontStyle": "normal", "foreground": "#dac58d"}}, {"name": "Selector", "scope": "meta.selector", "settings": {"foreground": "#ffb4a1"}}, {"name": "Headings", "scope": "markup.heading", "settings": {"foreground": "#ffb4ab"}}, {"name": "Headings", "scope": "markup.heading punctuation.definition.heading, entity.name.section", "settings": {"foreground": "#ffb4a1"}}, {"name": "Units", "scope": "keyword.other.unit", "settings": {"foreground": "#ffb4ab"}}, {"name": "Bold", "scope": "markup.bold,todo.bold", "settings": {"foreground": "#dac58d"}}, {"name": "Bold", "scope": "punctuation.definition.bold", "settings": {"foreground": "#e7bdb3"}}, {"name": "markup Italic", "scope": "markup.italic, punctuation.definition.italic,todo.emphasis", "settings": {"foreground": "#ffb4a1"}}, {"name": "emphasis md", "scope": "emphasis md", "settings": {"foreground": "#ffb4a1"}}, {"name": "[VSCODE-CUSTOM] Markdown headings", "scope": "entity.name.section.markdown", "settings": {"foreground": "#ffb4ab"}}, {"name": "[VSCODE-CUSTOM] Markdown heading Punctuation Definition", "scope": "punctuation.definition.heading.markdown", "settings": {"foreground": "#ffb4ab"}}, {"name": "punctuation.definition.list.begin.markdown", "scope": "punctuation.definition.list.begin.markdown", "settings": {"foreground": "#ffb4ab"}}, {"name": "[VSCODE-CUSTOM] Markdown heading setext", "scope": "markup.heading.setext", "settings": {"foreground": "#f1dfda"}}, {"name": "[VSCODE-CUSTOM] Markdown Punctuation Definition Bold", "scope": "punctuation.definition.bold.markdown", "settings": {"foreground": "#dac58d"}}, {"name": "[VSCODE-CUSTOM] Markdown Inline Raw", "scope": "markup.inline.raw.markdown", "settings": {"foreground": "#e7bdb3"}}, {"name": "[VSCODE-CUSTOM] Markdown Inline Raw", "scope": "markup.inline.raw.string.markdown", "settings": {"foreground": "#e7bdb3"}}, {"name": "[VSCODE-CUSTOM] Markdown Punctuation Definition Italic", "scope": "punctuation.definition.italic.markdown", "settings": {"foreground": "#ffb4a1"}}, {"name": "[VSCODE-CUSTOM] Markdown List Punctuation Definition", "scope": "beginning.punctuation.definition.list.markdown", "settings": {"foreground": "#ffb4ab"}}, {"name": "[VSCODE-CUSTOM] Markdown Quote", "scope": "markup.quote.markdown", "settings": {"foreground": "#a6928d", "fontStyle": "italic"}}, {"name": "[VSCODE-CUSTOM] Markdown Punctuation Definition String", "scope": ["punctuation.definition.string.begin.markdown", "punctuation.definition.string.end.markdown", "punctuation.definition.metadata.markdown"], "settings": {"foreground": "#f1dfda"}}, {"name": "[VSCODE-CUSTOM] Markdown Punctuation Definition Link", "scope": "punctuation.definition.metadata.markdown", "settings": {"foreground": "#ffb4a1"}}, {"name": "[VSCODE-CUSTOM] Markdown Underline Link/Image", "scope": "markup.underline.link.markdown,markup.underline.link.image.markdown", "settings": {"foreground": "#ffb4a1"}}, {"name": "[VSCODE-CUSTOM] Markdown Link Title/Description", "scope": "string.other.link.title.markdown,string.other.link.description.markdown", "settings": {"foreground": "#ffb4a1"}}, {"name": "Escape Characters", "scope": "constant.character.escape", "settings": {"foreground": "#ffb4a1"}}, {"name": "Embedded", "scope": "punctuation.section.embedded, variable.interpolation", "settings": {"foreground": "#ffb4ab"}}, {"name": "Embedded", "scope": "punctuation.section.embedded.begin,punctuation.section.embedded.end", "settings": {"foreground": "#ffb4a1"}}, {"name": "illegal", "scope": "invalid.illegal", "settings": {"foreground": "#f1dfda"}}, {"name": "illegal", "scope": "invalid.illegal.bad-ampersand.html", "settings": {"foreground": "#f1dfda"}}, {"name": "Broken", "scope": "invalid.broken", "settings": {"foreground": "#f1dfda"}}, {"name": "Deprecated", "scope": "invalid.deprecated", "settings": {"foreground": "#f1dfda"}}, {"name": "Unimplemented", "scope": "invalid.unimplemented", "settings": {"foreground": "#f1dfda"}}, {"name": "Source Json Meta Structure Dictionary Json > String Quoted <PERSON><PERSON>", "scope": "source.json meta.structure.dictionary.json > string.quoted.json", "settings": {"foreground": "#ffb4ab"}}, {"name": "Source Json Meta Structure Dictionary Json > String Quoted J<PERSON> > Punctuation String", "scope": "source.json meta.structure.dictionary.json > string.quoted.json > punctuation.string", "settings": {"foreground": "#ffb4ab"}}, {"name": "Source Json Meta Structure Dictionary Json > Value Json > String Quoted Json,source Json Meta Structure Array Json > Value Json > String Quoted Json,source Json Meta Structure Dictionary Json > Value Json > String Quoted Json > Punctuation,source Json Meta Structure Array Json > Value Json > String Quoted Json > Punctuation", "scope": "source.json meta.structure.dictionary.json > value.json > string.quoted.json,source.json meta.structure.array.json > value.json > string.quoted.json,source.json meta.structure.dictionary.json > value.json > string.quoted.json > punctuation,source.json meta.structure.array.json > value.json > string.quoted.json > punctuation", "settings": {"foreground": "#e7bdb3"}}, {"name": "Source Json Meta Structure Dictionary Json > Constant Language Json,source Json Meta Structure Array Json > Constant Language Json", "scope": "source.json meta.structure.dictionary.json > constant.language.json,source.json meta.structure.array.json > constant.language.json", "settings": {"foreground": "#ffb4a1"}}, {"name": "ts primitive/builtin types", "scope": "support.type.primitive.ts,support.type.builtin.ts,support.type.primitive.tsx,support.type.builtin.tsx", "settings": {"foreground": "#e7bdb3"}}, {"name": "block scope", "scope": "block.scope.end,block.scope.begin", "settings": {"foreground": "#f1dfda"}}, {"name": "cs storage type", "scope": "storage.type.cs", "settings": {"foreground": "#e7bdb3"}}, {"name": "cs local variable", "scope": "entity.name.variable.local.cs", "settings": {"foreground": "#ffb4ab"}}, {"scope": "token.info-token", "settings": {"foreground": "#ffb4a1"}}, {"scope": "token.warn-token", "settings": {"foreground": "#dac58d"}}, {"scope": "token.error-token", "settings": {"foreground": "#ffb4ab"}}, {"scope": "token.debug-token", "settings": {"foreground": "#ffb4a1"}}, {"name": "String interpolation", "scope": ["punctuation.definition.template-expression.begin", "punctuation.definition.template-expression.end", "punctuation.section.embedded"], "settings": {"foreground": "#ffb4a1"}}, {"name": "Reset JavaScript string interpolation expression", "scope": ["meta.template.expression"], "settings": {"foreground": "#f1dfda"}}, {"name": "Import module JS", "scope": ["keyword.operator.module"], "settings": {"foreground": "#ffb4a1"}}, {"name": "js Flowtype", "scope": ["support.type.type.flowtype"], "settings": {"foreground": "#ffb4a1"}}, {"name": "js Flow", "scope": ["support.type.primitive"], "settings": {"foreground": "#e7bdb3"}}, {"name": "js class prop", "scope": ["meta.property.object"], "settings": {"foreground": "#ffb4ab"}}, {"name": "js func parameter", "scope": ["variable.parameter.function.js"], "settings": {"foreground": "#ffb4ab"}}, {"name": "js template literals begin", "scope": ["keyword.other.template.begin"], "settings": {"foreground": "#e7bdb3"}}, {"name": "js template literals end", "scope": ["keyword.other.template.end"], "settings": {"foreground": "#e7bdb3"}}, {"name": "js template literals variable braces begin", "scope": ["keyword.other.substitution.begin"], "settings": {"foreground": "#e7bdb3"}}, {"name": "js template literals variable braces end", "scope": ["keyword.other.substitution.end"], "settings": {"foreground": "#e7bdb3"}}, {"name": "js operator.assignment", "scope": ["keyword.operator.assignment"], "settings": {"foreground": "#ffb4a1"}}, {"name": "go operator", "scope": ["keyword.operator.assignment.go"], "settings": {"foreground": "#e7bdb3"}}, {"name": "go operator", "scope": ["keyword.operator.arithmetic.go", "keyword.operator.address.go"], "settings": {"foreground": "#ffb4a1"}}, {"name": "js/ts italic", "scope": "entity.other.attribute-name.js,entity.other.attribute-name.ts,entity.other.attribute-name.jsx,entity.other.attribute-name.tsx,variable.parameter,variable.language.super", "settings": {"fontStyle": "italic"}}, {"name": "comment", "scope": "comment.line.double-slash,comment.block.documentation", "settings": {"fontStyle": "italic"}}, {"name": "Python Keyword Control", "scope": "keyword.control.import.python,keyword.control.flow.python", "settings": {"fontStyle": "italic"}}, {"name": "markup.italic.markdown", "scope": "markup.italic.markdown", "settings": {"fontStyle": "italic"}}, {"name": "css color standard name", "scope": "support.constant.color.w3c-standard-color-name.css,support.constant.color.w3c-standard-color-name.scss", "settings": {"foreground": "#dac58d"}}, {"name": "css comma", "scope": "punctuation.separator.list.comma.css", "settings": {"foreground": "#f1dfda"}}, {"name": "css attribute-name.id", "scope": "support.constant.color.w3c-standard-color-name.css", "settings": {"foreground": "#dac58d"}}, {"name": "css property-name", "scope": "support.type.vendored.property-name.css", "settings": {"foreground": "#ffb4a1"}}, {"name": "js/ts module", "scope": "support.module.node,support.type.object.module,support.module.node", "settings": {"foreground": "#e7bdb3"}}, {"name": "entity.name.type.module", "scope": "entity.name.type.module", "settings": {"foreground": "#e7bdb3"}}, {"name": "js variable readwrite", "scope": "variable.other.readwrite,meta.object-literal.key,support.variable.property,support.variable.object.process,support.variable.object.node", "settings": {"foreground": "#ffb4ab"}}, {"name": "js/ts json", "scope": "support.constant.json", "settings": {"foreground": "#dac58d"}}, {"name": "js/ts Keyword", "scope": ["keyword.operator.expression.instanceof", "keyword.operator.new", "keyword.operator.ternary", "keyword.operator.optional", "keyword.operator.expression.keyof"], "settings": {"foreground": "#ffb4a1"}}, {"name": "js/ts console", "scope": "support.type.object.console", "settings": {"foreground": "#ffb4ab"}}, {"name": "js/ts support.variable.property.process", "scope": "support.variable.property.process", "settings": {"foreground": "#dac58d"}}, {"name": "js console function", "scope": "entity.name.function,support.function.console", "settings": {"foreground": "#ffb4a1"}}, {"name": "keyword.operator.misc.rust", "scope": "keyword.operator.misc.rust", "settings": {"foreground": "#f1dfda"}}, {"name": "keyword.operator.sigil.rust", "scope": "keyword.operator.sigil.rust", "settings": {"foreground": "#ffb4a1"}}, {"name": "operator", "scope": "keyword.operator.delete", "settings": {"foreground": "#ffb4a1"}}, {"name": "js dom", "scope": "support.type.object.dom", "settings": {"foreground": "#ffb4a1"}}, {"name": "js dom variable", "scope": "support.variable.dom,support.variable.property.dom", "settings": {"foreground": "#ffb4ab"}}, {"name": "python placeholder reset to normal string", "scope": "constant.character.format.placeholder.other.python", "settings": {"foreground": "#dac58d"}}, {"name": "Operators", "scope": "keyword.operator", "settings": {"foreground": "#f1dfda"}}, {"name": "Compound Assignment Operators", "scope": "keyword.operator.assignment.compound", "settings": {"foreground": "#ffb4a1"}}, {"name": "Compound Assignment Operators js/ts", "scope": "keyword.operator.assignment.compound.js,keyword.operator.assignment.compound.ts", "settings": {"foreground": "#ffb4a1"}}, {"name": "Keywords", "scope": "keyword", "settings": {"foreground": "#ffb4a1"}}, {"name": "Namespaces", "scope": "entity.name.namespace", "settings": {"foreground": "#e7bdb3"}}, {"name": "Variables", "scope": "variable", "settings": {"foreground": "#ffb4ab"}}, {"name": "Variables", "scope": "variable.c", "settings": {"foreground": "#f1dfda"}}, {"name": "Language variables", "scope": "variable.language", "settings": {"foreground": "#e7bdb3"}}, {"name": "Functions", "scope": ["entity.name.function", "meta.require", "support.function.any-method", "variable.function"], "settings": {"foreground": "#ffb4a1"}}, {"name": "Classes", "scope": "entity.name.type.namespace", "settings": {"foreground": "#e7bdb3"}}, {"name": "Classes", "scope": "support.class, entity.name.type.class", "settings": {"foreground": "#e7bdb3"}}, {"name": "Class name", "scope": "entity.name.class.identifier.namespace.type", "settings": {"foreground": "#e7bdb3"}}, {"name": "Class name", "scope": ["entity.name.class", "variable.other.class.js", "variable.other.class.ts"], "settings": {"foreground": "#e7bdb3"}}, {"name": "Storage", "scope": "storage", "settings": {"foreground": "#ffb4a1"}}, {"name": "Storage JS TS", "scope": "token.storage", "settings": {"foreground": "#ffb4a1"}}, {"name": "Source Js Keyword Operator Delete,source Js Keyword Operator In,source Js Keyword Operator Of,source Js Keyword Operator Instanceof,source Js Keyword Operator New,source Js Keyword Operator Typeof,source Js Keyword Operator Void", "scope": "keyword.operator.expression.delete,keyword.operator.expression.in,keyword.operator.expression.of,keyword.operator.expression.instanceof,keyword.operator.new,keyword.operator.expression.typeof,keyword.operator.expression.void", "settings": {"foreground": "#ffb4a1"}}, {"name": "Support type", "scope": "support.type.property-name", "settings": {"foreground": "#f1dfda"}}, {"name": "Support type", "scope": "support.constant.property-value", "settings": {"foreground": "#f1dfda"}}, {"name": "Support type", "scope": "support.constant.font-name", "settings": {"foreground": "#dac58d"}}, {"name": "Regular Expressions", "scope": "string.regexp", "settings": {"foreground": "#ffb4a1"}}, {"name": "Go package name", "scope": ["entity.name.package.go"], "settings": {"foreground": "#e7bdb3"}}, {"name": "elm prelude", "scope": ["support.type.prelude.elm"], "settings": {"foreground": "#ffb4a1"}}, {"name": "elm constant", "scope": ["support.constant.elm"], "settings": {"foreground": "#dac58d"}}, {"name": "template literal", "scope": ["punctuation.quasi.element"], "settings": {"foreground": "#ffb4a1"}}, {"name": "html/pug (jade) escaped characters and entities", "scope": ["constant.character.entity"], "settings": {"foreground": "#ffb4ab"}}, {"name": "styling css pseudo-elements/classes to be able to differentiate from classes which are the same colour", "scope": ["entity.other.attribute-name.pseudo-element", "entity.other.attribute-name.pseudo-class"], "settings": {"foreground": "#ffb4a1"}}, {"name": "[VSCODE-CUSTOM] JSON Property Name", "scope": "support.type.property-name.json", "settings": {"foreground": "#ffb4ab"}}, {"name": "[VSCODE-CUSTOM] JSON Punctuation for Property Name", "scope": "support.type.property-name.json punctuation", "settings": {"foreground": "#ffb4ab"}}, {"name": "Comments", "scope": "comment, punctuation.definition.comment", "settings": {"fontStyle": "italic", "foreground": "#a6928d"}}, {"name": "punctuation.definition.block.sequence.item.yaml", "scope": "punctuation.definition.block.sequence.item.yaml", "settings": {"foreground": "#f1dfda"}}, {"scope": ["constant.language.symbol.elixir"], "settings": {"foreground": "#ffb4a1"}}, {"name": "python block", "scope": "punctuation.definition.arguments.begin.python,punctuation.definition.arguments.end.python,punctuation.separator.arguments.python,punctuation.definition.list.begin.python,punctuation.definition.list.end.python", "settings": {"foreground": "#f1dfda"}}, {"name": "python function-call.generic", "scope": "meta.function-call.generic.python", "settings": {"foreground": "#ffb4a1"}}, {"name": "pyCs", "scope": "variable.parameter.function.python", "settings": {"foreground": "#dac58d"}}, {"name": "python function decorator @", "scope": "meta.function.decorator.python", "settings": {"foreground": "#ffb4a1"}}, {"name": "python function support", "scope": "support.token.decorator.python,meta.function.decorator.identifier.python", "settings": {"foreground": "#ffb4a1"}}, {"name": "parameter function js/ts", "scope": "function.parameter", "settings": {"foreground": "#f1dfda"}}, {"name": "brace function", "scope": "function.brace", "settings": {"foreground": "#f1dfda"}}, {"name": "parameter function ruby cs", "scope": "function.parameter.ruby, function.parameter.cs", "settings": {"foreground": "#f1dfda"}}, {"name": "constant.language.symbol.ruby", "scope": "constant.language.symbol.ruby", "settings": {"foreground": "#ffb4a1"}}, {"name": "rgb-value", "scope": "rgb-value", "settings": {"foreground": "#ffb4a1"}}, {"name": "rgb value", "scope": "inline-color-decoration rgb-value", "settings": {"foreground": "#dac58d"}}, {"name": "rgb value less", "scope": "less rgb-value", "settings": {"foreground": "#dac58d"}}, {"name": "Clojure globals", "scope": ["entity.global.clojure"], "settings": {"foreground": "#e7bdb3"}}, {"name": "Clojure symbols", "scope": ["meta.symbol.clojure"], "settings": {"foreground": "#ffb4ab"}}, {"name": "Clojure constants", "scope": ["constant.keyword.clojure"], "settings": {"foreground": "#ffb4a1"}}, {"name": "CoffeeScript Function Argument", "scope": ["meta.arguments.coffee", "variable.parameter.function.coffee"], "settings": {"foreground": "#ffb4ab"}}, {"name": "<PERSON><PERSON> Default Text", "scope": ["source.ini"], "settings": {"foreground": "#e7bdb3"}}, {"name": "Makefile prerequisities", "scope": ["meta.scope.prerequisites.makefile"], "settings": {"foreground": "#ffb4ab"}}, {"name": "Makefile text colour", "scope": ["source.makefile"], "settings": {"foreground": "#e7bdb3"}}, {"name": "Groovy import names", "scope": ["storage.modifier.import.groovy"], "settings": {"foreground": "#e7bdb3"}}, {"name": "Groovy Methods", "scope": ["meta.method.groovy"], "settings": {"foreground": "#ffb4a1"}}, {"name": "Groovy Variables", "scope": ["meta.definition.variable.name.groovy"], "settings": {"foreground": "#ffb4ab"}}, {"name": "Groovy Inheritance", "scope": ["meta.definition.class.inherited.classes.groovy"], "settings": {"foreground": "#e7bdb3"}}, {"name": "HLSL Semantic", "scope": ["support.variable.semantic.hlsl"], "settings": {"foreground": "#e7bdb3"}}, {"name": "HLSL Types", "scope": ["support.type.texture.hlsl", "support.type.sampler.hlsl", "support.type.object.hlsl", "support.type.object.rw.hlsl", "support.type.fx.hlsl", "support.type.object.hlsl"], "settings": {"foreground": "#ffb4a1"}}, {"name": "SQL Variables", "scope": ["text.variable", "text.bracketed"], "settings": {"foreground": "#ffb4ab"}}, {"name": "types", "scope": ["support.type.swift", "support.type.vb.asp"], "settings": {"foreground": "#e7bdb3"}}, {"name": "Java Variables", "scope": "token.variable.parameter.java", "settings": {"foreground": "#f1dfda"}}, {"name": "Java Imports", "scope": "import.storage.java", "settings": {"foreground": "#e7bdb3"}}, {"name": "Packages", "scope": "token.package.keyword", "settings": {"foreground": "#ffb4a1"}}, {"name": "Packages", "scope": "token.package", "settings": {"foreground": "#f1dfda"}}, {"name": "Type Name", "scope": "entity.name.type", "settings": {"foreground": "#e7bdb3"}}, {"name": "Keyword Control", "scope": "keyword.control", "settings": {"foreground": "#ffb4a1"}}, {"name": "Control Elements", "scope": "control.elements, keyword.operator.less", "settings": {"foreground": "#dac58d"}}, {"name": "Methods", "scope": "keyword.other.special-method", "settings": {"foreground": "#ffb4a1"}}, {"name": "Java Storage", "scope": "token.storage.type.java", "settings": {"foreground": "#e7bdb3"}}, {"name": "Support", "scope": "support.function", "settings": {"foreground": "#ffb4a1"}}, {"name": "Class name php", "scope": "variable.other.class.php", "settings": {"foreground": "#ffb4ab"}}, {"name": "laravel blade tag", "scope": "text.html.laravel-blade source.php.embedded.line.html entity.name.tag.laravel-blade", "settings": {"foreground": "#ffb4a1"}}, {"name": "laravel blade @", "scope": "text.html.laravel-blade source.php.embedded.line.html support.constant.laravel-blade", "settings": {"foreground": "#ffb4a1"}}, {"name": "use statement for other classes", "scope": "support.other.namespace.use.php,support.other.namespace.use-as.php,support.other.namespace.php,entity.other.alias.php,meta.interface.php", "settings": {"foreground": "#e7bdb3"}}, {"name": "error suppression", "scope": "keyword.operator.error-control.php", "settings": {"foreground": "#ffb4a1"}}, {"name": "php instanceof", "scope": "keyword.operator.type.php", "settings": {"foreground": "#ffb4a1"}}, {"name": "style double quoted array index normal begin", "scope": "punctuation.section.array.begin.php", "settings": {"foreground": "#f1dfda"}}, {"name": "style double quoted array index normal end", "scope": "punctuation.section.array.end.php", "settings": {"foreground": "#f1dfda"}}, {"name": "php illegal.non-null-typehinted", "scope": "invalid.illegal.non-null-typehinted.php", "settings": {"foreground": "#ffb4ab"}}, {"name": "php types", "scope": "storage.type.php,meta.other.type.phpdoc.php,keyword.other.type.php,keyword.other.array.phpdoc.php", "settings": {"foreground": "#e7bdb3"}}, {"name": "php call-function", "scope": "meta.function-call.php,meta.function-call.object.php,meta.function-call.static.php", "settings": {"foreground": "#ffb4a1"}}, {"name": "php function-resets", "scope": "punctuation.definition.parameters.begin.bracket.round.php,punctuation.definition.parameters.end.bracket.round.php,punctuation.separator.delimiter.php,punctuation.section.scope.begin.php,punctuation.section.scope.end.php,punctuation.terminator.expression.php,punctuation.definition.arguments.begin.bracket.round.php,punctuation.definition.arguments.end.bracket.round.php,punctuation.definition.storage-type.begin.bracket.round.php,punctuation.definition.storage-type.end.bracket.round.php,punctuation.definition.array.begin.bracket.round.php,punctuation.definition.array.end.bracket.round.php,punctuation.definition.begin.bracket.round.php,punctuation.definition.end.bracket.round.php,punctuation.definition.begin.bracket.curly.php,punctuation.definition.end.bracket.curly.php,punctuation.definition.section.switch-block.end.bracket.curly.php,punctuation.definition.section.switch-block.start.bracket.curly.php,punctuation.definition.section.switch-block.begin.bracket.curly.php,punctuation.definition.section.switch-block.end.bracket.curly.php", "settings": {"foreground": "#f1dfda"}}, {"name": "support php constants", "scope": "support.constant.core.rust", "settings": {"foreground": "#dac58d"}}, {"name": "support php constants", "scope": "support.constant.ext.php,support.constant.std.php,support.constant.core.php,support.constant.parser-token.php", "settings": {"foreground": "#dac58d"}}, {"name": "php goto", "scope": "entity.name.goto-label.php,support.other.php", "settings": {"foreground": "#ffb4a1"}}, {"name": "php logical/bitwise operator", "scope": "keyword.operator.logical.php,keyword.operator.bitwise.php,keyword.operator.arithmetic.php", "settings": {"foreground": "#ffb4a1"}}, {"name": "php regexp operator", "scope": "keyword.operator.regexp.php", "settings": {"foreground": "#ffb4a1"}}, {"name": "php comparison", "scope": "keyword.operator.comparison.php", "settings": {"foreground": "#ffb4a1"}}, {"name": "php heredoc/nowdoc", "scope": "keyword.operator.heredoc.php,keyword.operator.nowdoc.php", "settings": {"foreground": "#ffb4a1"}}], "colors": {"foreground": "#f1dfda", "focusBorder": "#ffb4a1", "selection.background": "#ffb4a166", "scrollbar.shadow": "#1a110f00", "activityBar.foreground": "#f5e3df", "activityBar.background": "#1a110f", "activityBar.inactiveForeground": "#d8c2bc", "activityBarBadge.foreground": "#4b1709", "activityBarBadge.background": "#ffb4a1", "sideBar.background": "#1a110f", "sideBar.foreground": "#f5e3df", "sideBarSectionHeader.background": "#53433f", "sideBarSectionHeader.foreground": "#d8c2bc", "sideBarTitle.foreground": "#ffb4a1", "list.inactiveSelectionBackground": "#53433f", "list.inactiveSelectionForeground": "#d8c2bc", "list.hoverBackground": "#53433f40", "list.hoverForeground": "#d8c2bc", "list.activeSelectionBackground": "#ffb4a140", "list.activeSelectionForeground": "#f5e3df", "tree.indentGuidesStroke": "#a6928d", "list.dropBackground": "#97513f40", "list.highlightForeground": "#ffb4a1", "list.focusBackground": "#ffb4a130", "list.focusForeground": "#f5e3df", "listFilterWidget.background": "#736334", "listFilterWidget.outline": "#a6928d00", "listFilterWidget.noMatchesOutline": "#ffb4ab", "statusBar.foreground": "#d8c2bc", "statusBar.background": "#53433f", "statusBarItem.hoverBackground": "#ffb4a11f", "statusBar.debuggingBackground": "#dac58d", "statusBar.debuggingForeground": "#322600", "statusBar.noFolderBackground": "#1a110f", "statusBar.noFolderForeground": "#f5e3df", "statusBarItem.remoteBackground": "#ffb4a1", "statusBarItem.remoteForeground": "#4b1709", "titleBar.activeBackground": "#1a110f", "titleBar.activeForeground": "#f5e3df", "titleBar.inactiveBackground": "#53433f", "titleBar.inactiveForeground": "#d8c2bc", "titleBar.border": "#a6928d00", "menubar.selectionForeground": "#f5e3df", "menubar.selectionBackground": "#53433f1a", "menu.foreground": "#f5e3df", "menu.background": "#1a110f", "menu.selectionForeground": "#ffffff", "menu.selectionBackground": "#97513f", "menu.selectionBorder": "#a6928d00", "menu.separatorBackground": "#a6928d", "menu.border": "#a6928d85", "button.background": "#ffb4a1", "button.foreground": "#4b1709", "button.hoverBackground": "#97513f", "button.secondaryForeground": "#3a211b", "button.secondaryBackground": "#e7bdb3", "button.secondaryHoverBackground": "#7e5d54", "checkbox.background": "#97513f", "checkbox.foreground": "#ffffff", "dropdown.background": "#1a110f", "dropdown.foreground": "#f5e3df", "input.background": "#53433f", "input.foreground": "#d8c2bc", "input.placeholderForeground": "#d8c2bc9e", "inputOption.activeBackground": "#ffb4a130", "inputOption.activeBorder": "#ffb4a130", "inputOption.activeForeground": "#ffb4a1", "inputValidation.errorBackground": "#c32220", "inputValidation.errorForeground": "#ffffff", "inputValidation.errorBorder": "#ffb4ab", "inputValidation.infoBackground": "#736334", "inputValidation.infoForeground": "#ffffff", "inputValidation.infoBorder": "#dac58d", "inputValidation.warningBackground": "#7e5d54", "inputValidation.warningForeground": "#ffffff", "inputValidation.warningBorder": "#e7bdb3", "scrollbarSlider.activeBackground": "#f5e3df80", "scrollbarSlider.background": "#f5e3df30", "scrollbarSlider.hoverBackground": "#f5e3df50", "badge.foreground": "#4b1709", "badge.background": "#ffb4a1", "progressBar.background": "#ffb4a1", "editor.background": "#1a110f", "editor.foreground": "#f1dfda", "editorLineNumber.foreground": "#a6928d", "editorLineNumber.activeForeground": "#ffb4a1", "editorCursor.background": "#1a110f", "editorCursor.foreground": "#ffb4a1", "editor.selectionBackground": "#ffb4a140", "editor.selectionHighlightBackground": "#53433f80", "editor.inactiveSelectionBackground": "#53433f", "editor.wordHighlightBackground": "#53433f", "editor.wordHighlightStrongBackground": "#97513f80", "editor.findMatchBackground": "#dac58d80", "editor.findMatchHighlightBackground": "#dac58d60", "editor.findRangeHighlightBackground": "#53433f", "editor.hoverHighlightBackground": "#53433f50", "editor.lineHighlightBackground": "#53433f50", "editor.lineHighlightBorder": "#a6928d00", "editorLink.activeForeground": "#ffb4a1", "editorIndentGuide.background": "#a6928d20", "editorIndentGuide.activeBackground": "#a6928d", "editorRuler.foreground": "#a6928d", "editorBracketMatch.background": "#ffb4a14d", "editorBracketMatch.border": "#ffb4a1", "editorBracketHighlight.foreground1": "#ffb4a1", "editorBracketHighlight.foreground2": "#e7bdb3", "editorBracketHighlight.foreground3": "#dac58d", "editorBracketHighlight.foreground4": "#97513f", "editorBracketHighlight.foreground5": "#7e5d54", "editorBracketHighlight.foreground6": "#736334", "editorBracketHighlight.unexpectedBracket.foreground": "#ffb4ab", "editorOverviewRuler.border": "#a6928d", "editorOverviewRuler.findMatchForeground": "#ffb4a1", "editorOverviewRuler.rangeHighlightForeground": "#ffb4a1", "editorOverviewRuler.selectionHighlightForeground": "#ffb4a1", "editorOverviewRuler.wordHighlightForeground": "#e7bdb3", "editorOverviewRuler.wordHighlightStrongForeground": "#e7bdb3", "editorOverviewRuler.modifiedForeground": "#ffb4a180", "editorOverviewRuler.addedForeground": "#e7bdb380", "editorOverviewRuler.deletedForeground": "#ffb4ab80", "editorOverviewRuler.errorForeground": "#ffb4ab", "editorOverviewRuler.warningForeground": "#e7bdb3", "editorOverviewRuler.infoForeground": "#dac58d", "editorGutter.background": "#1a110f", "editorGutter.modifiedBackground": "#ffb4a180", "editorGutter.addedBackground": "#e7bdb380", "editorGutter.deletedBackground": "#ffb4ab80", "editorCodeLens.foreground": "#a6928d", "editorGroup.border": "#a6928d", "editorGroup.dropBackground": "#97513f40", "editorGroupHeader.noTabsBackground": "#1a110f", "editorGroupHeader.tabsBackground": "#1a110f", "editorGroupHeader.tabsBorder": "#a6928d00", "editorWidget.foreground": "#f5e3df", "editorWidget.background": "#1a110f", "editorWidget.border": "#a6928d", "editorWidget.resizeBorder": "#a6928d", "editorSuggestWidget.background": "#1a110f", "editorSuggestWidget.border": "#a6928d", "editorSuggestWidget.foreground": "#f5e3df", "editorSuggestWidget.highlightForeground": "#ffb4a1", "editorSuggestWidget.selectedBackground": "#53433f", "editorHoverWidget.foreground": "#f5e3df", "editorHoverWidget.background": "#1a110f", "editorHoverWidget.border": "#a6928d", "editorMarkerNavigation.background": "#1a110f", "editorMarkerNavigationError.background": "#ffb4ab40", "editorMarkerNavigationWarning.background": "#e7bdb340", "editorMarkerNavigationInfo.background": "#dac58d40", "peekView.border": "#ffb4a1", "peekViewEditor.background": "#1a110f", "peekViewEditor.matchHighlightBackground": "#dac58d99", "peekViewResult.background": "#53433f", "peekViewResult.fileForeground": "#d8c2bc", "peekViewResult.lineForeground": "#d8c2bc", "peekViewResult.matchHighlightBackground": "#dac58d40", "peekViewResult.selectionBackground": "#ffb4a140", "peekViewResult.selectionForeground": "#ffffff", "peekViewTitle.background": "#53433f", "peekViewTitleDescription.foreground": "#d8c2bc", "peekViewTitleLabel.foreground": "#ffb4a1", "merge.currentHeaderBackground": "#ffb4a130", "merge.currentContentBackground": "#ffb4a120", "merge.incomingHeaderBackground": "#dac58d30", "merge.incomingContentBackground": "#dac58d20", "merge.commonHeaderBackground": "#a6928d30", "merge.commonContentBackground": "#a6928d20", "merge.border": "#a6928d00", "panel.background": "#1a110f", "panel.border": "#a6928d", "panel.dropBorder": "#ffb4a1", "panelTitle.activeBorder": "#ffb4a1", "panelTitle.activeForeground": "#ffb4a1", "panelTitle.inactiveForeground": "#d8c2bc", "tab.activeBackground": "#1a110f", "tab.activeForeground": "#ffb4a1", "tab.border": "#a6928d00", "tab.activeBorder": "#ffb4a1", "tab.inactiveBackground": "#53433f", "tab.inactiveForeground": "#d8c2bc", "tab.unfocusedActiveForeground": "#ffb4a199", "tab.unfocusedInactiveForeground": "#d8c2bc99", "tab.unfocusedActiveBorder": "#ffb4a180", "tab.unfocusedActiveBackground": "#1a110f99", "tab.unfocusedInactiveBackground": "#53433f99", "tab.activeModifiedBorder": "#dac58d", "tab.inactiveModifiedBorder": "#dac58d80", "tab.unfocusedActiveModifiedBorder": "#dac58d40", "tab.unfocusedInactiveModifiedBorder": "#dac58d20", "terminal.background": "#1a110f", "terminal.foreground": "#f1dfda", "terminal.ansiBlack": "#a6928d", "terminal.ansiRed": "#ffb4ab", "terminal.ansiGreen": "#e7bdb3", "terminal.ansiYellow": "#7e5d54", "terminal.ansiBlue": "#ffb4a1", "terminal.ansiMagenta": "#dac58d", "terminal.ansiCyan": "#736334", "terminal.ansiWhite": "#f5e3df", "terminal.ansiBrightBlack": "#d8c2bc", "terminal.ansiBrightRed": "#c32220", "terminal.ansiBrightGreen": "#7e5d54", "terminal.ansiBrightYellow": "#e7bdb3", "terminal.ansiBrightBlue": "#97513f", "terminal.ansiBrightMagenta": "#736334", "terminal.ansiBrightCyan": "#dac58d", "terminal.ansiBrightWhite": "#f1dfda", "terminalCursor.background": "#1a110f", "terminalCursor.foreground": "#ffb4a1", "notificationCenter.border": "#a6928d", "notificationCenterHeader.background": "#53433f", "notificationCenterHeader.foreground": "#d8c2bc", "notificationToast.border": "#a6928d", "notifications.foreground": "#f5e3df", "notifications.background": "#1a110f", "notifications.border": "#a6928d", "notificationsErrorIcon.foreground": "#ffb4ab", "notificationsWarningIcon.foreground": "#e7bdb3", "notificationsInfoIcon.foreground": "#dac58d", "extensionButton.prominentForeground": "#4b1709", "extensionButton.prominentBackground": "#ffb4a1", "extensionButton.prominentHoverBackground": "#97513f", "pickerGroup.border": "#a6928d", "pickerGroup.foreground": "#ffb4a1", "debugToolBar.background": "#1a110f", "debugToolBar.border": "#a6928d", "welcomePage.buttonBackground": "#53433f", "welcomePage.buttonHoverBackground": "#ffb4a120", "walkThrough.embeddedEditorBackground": "#1a110f50", "gitDecoration.modifiedResourceForeground": "#ffb4a1c0", "gitDecoration.deletedResourceForeground": "#ffb4abc0", "gitDecoration.untrackedResourceForeground": "#e7bdb3c0", "gitDecoration.ignoredResourceForeground": "#a6928d80", "gitDecoration.conflictingResourceForeground": "#dac58dc0", "gitDecoration.submoduleResourceForeground": "#d8c2bcc0", "settings.headerForeground": "#f1dfda", "settings.modifiedItemIndicator": "#ffb4a1", "settings.dropdownBackground": "#53433f", "settings.dropdownForeground": "#d8c2bc", "settings.dropdownBorder": "#a6928d", "settings.checkboxBackground": "#53433f", "settings.checkboxForeground": "#d8c2bc", "settings.checkboxBorder": "#a6928d", "settings.textInputBackground": "#53433f", "settings.textInputForeground": "#d8c2bc", "settings.textInputBorder": "#a6928d", "settings.numberInputBackground": "#53433f", "settings.numberInputForeground": "#d8c2bc", "settings.numberInputBorder": "#a6928d", "breadcrumb.foreground": "#d8c2bc", "breadcrumb.background": "#1a110f", "breadcrumb.focusForeground": "#f5e3df", "breadcrumb.activeSelectionForeground": "#ffb4a1", "breadcrumbPicker.background": "#1a110f", "diffEditor.insertedTextBackground": "#e7bdb320", "diffEditor.removedTextBackground": "#ffb4ab20", "diffEditor.diagonalFill": "#a6928d40", "debugExceptionWidget.background": "#1a110f", "debugExceptionWidget.border": "#a6928d", "editorGutter.commentRangeForeground": "#d8c2bc", "icon.foreground": "#f1dfda", "minimapGutter.addedBackground": "#e7bdb380", "minimapGutter.modifiedBackground": "#ffb4a180", "minimapGutter.deletedBackground": "#ffb4ab80", "minimap.findMatchHighlight": "#ffb4a180", "minimap.selectionHighlight": "#ffb4a140", "minimap.errorHighlight": "#ffb4ab80", "minimap.warningHighlight": "#e7bdb380", "minimap.background": "#1a110f", "sideBar.dropBackground": "#53433f40", "editorGroup.emptyBackground": "#1a110f", "panelSection.border": "#a6928d", "statusBarItem.activeBackground": "#ffb4a125", "settings.focusedRowBackground": "#53433f50", "editorGutter.foldingControlForeground": "#d8c2bc", "editor.foldBackground": "#ffb4a14d", "editorError.foreground": "#ffb4ab", "editorError.background": "#c3222000", "editorError.border": "#a6928d00", "editorWarning.foreground": "#e7bdb3", "editorWarning.background": "#7e5d5400", "editorWarning.border": "#a6928d00", "editorInfo.foreground": "#dac58d", "editorInfo.background": "#73633400", "editorInfo.border": "#a6928d00", "editorWhitespace.foreground": "#a6928d29", "widget.shadow": "#1a110f5c", "peekViewEditorGutter.background": "#1a110f", "peekViewEditor.matchHighlightBorder": "#dac58d", "input.border": "#a6928d00", "textLink.foreground": "#ffb4a1", "textLink.activeForeground": "#97513f"}}