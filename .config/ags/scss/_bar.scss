// 1rem = 11pt = 14.6666666667px
$bar_ws_width: 1.774rem;
$bar_ws_width_focus: 1rem;
$bar_ws_width_focus_active: 6.045rem;

@mixin bar-group-rounding {
    @include small-rounding;
}

.bar-module-box {
    background-color: $layer0;
    color: $onLayer0;
    @include full-rounding;
    padding: 0.5rem 1.5rem;
}

.bar-icon {
    @include icon-material;
    @include full-rounding;
    background-color: $layer0;
    color: $onLayer0;
    font-size: 2.032rem;
}

.bar-util-btn2 {
    @include element-decel;
    background-color: $layer0;
    min-width: 1rem;
    @include full-rounding;
    border: 0.6px solid $outliner;
}

.bar-notch {
    background-color: $layer0;
    min-width: 16.1rem;
    border-radius: 0 0 $rounding_mediumlarge $rounding_mediumlarge;
    /* Removed bottom border */
}

.bar-resources {
    progressbar {
        @include element_decel;

        // min-height: 0.2rem;
        trough {
            @include full-rounding;
            // min-height: 0.2rem;
            min-width: 6rem;
            background-color: $surfaceVariant;

        }

        progress {
            padding: 0 0;
            background-color: $primary;
            @include full-rounding;
        }
    }

    &:first-child progressbar {
        progress {
            background-color: $primary;
        }
    }

    &:nth-child(2) progressbar {
        progress {
            background-color: mix($primary, $onSurface, 80%);
        }
    }

    &:last-child progressbar {
        progress {
            background-color: mix($primary, $onSurface, 60%);
        }
    }
}

.bar-knocks {
    @include menu_accel_fast;
    border-radius: $rounding_mediumlarge;
    margin: 0.6rem 0;
    border: 0.4px solid $outliner;
    background-color: $layer0;
    padding: 0rem 1rem;
    box-shadow:
        0 4px 2px rgba($shadow, 0.15),
        /* Lighter, broader blur for depth */
        0 3px 4px rgba($shadow, 0.2),
        /* Subtle mid-distance shadow */
    ;

    &.song-changing {
        animation: multiSweep 2.2s linear;
        animation-iteration-count: 13;
    }
}

.bar-floating {
    background-color: $layer0;
    @include elevation2;
    border: 0.4px solid rgba($outliner, 0.6);
    @include anim-enter;
    @include normal-rounding;
    box-shadow:
        0 5px 3px rgba($shadow, 0.15),
        /* Lighter, broader blur for depth */
        0 8px 6px rgba($shadow, 0.2),
        /* Subtle mid-distance shadow */
        // 0 20px 28px rgba(0, 0, 0, 0.05),  /* Subtle mid-distance shadow */

}

.bar-floating-outline {
    background-color: $layer0;
    @include elevation2;
    // border: 0.5px solid rgba($onSurfaceVariant, 0.5);
    @include anim-enter;
    @include small-rounding;
}

.bar-height {
    min-height: 2.827rem;
}

.prim-txt {
    color: $secondary;
}

.bar-bg {
    background-color: $layer0;
    min-height: 2.827rem;
}

.bar-vertical-pinned {
    background-color: $background;
    margin-right: 0.3rem;
}

.bar-pads {
    background-color: $layer0;
    @include large-rounding;
}

.bar-bg-focus {
    background-color: $layer0;
    min-height: 1.364rem;
}

.bar-bg-nothing {
    background-color: $layer0;
    min-height: 2px;
}

.bar-bg-focus-batterylow {
    background-color: mix($layer0, $errorContainer, 80%);
}

.bar-sidespace {
    min-width: 1.5rem;
}

.bar-group-margin {
    padding: 0.273rem 0rem;
}

.bar-group {
    background-color: $layer1;
}

.bar-saadi {
    background-color: $background;
}

.group-saadi {
    @include full-rounding;
    padding: 0 1rem;
    margin: 0.6rem 0.4rem;
    background-color: $layer1;
}

.bar-group2 {
    @include titlefont;
    color: $battOnLayer2;
    @include full-rounding;
    padding: 0 1.3rem;
    background-color: $battLayer2;
}

.bar-gradiant-tert {
    @include full-rounding;
    padding: 0 1rem;
    margin: 0.6rem 0.4rem;
    background: linear-gradient(90deg, $surfaceContainerHighest, $tertiaryFixed);
}

.bar-gradiant-prim {
    @include full-rounding;
    padding: 0 1rem;
    margin: 0.6rem 0.4rem;
    background: linear-gradient(90deg, $surfaceContainerHighest, $primaryFixed);
}

.bar-gradiant-sec {
    @include full-rounding;
    padding: 0 1rem;
    margin: 0.6rem 0.4rem;
    background: linear-gradient(90deg, $surfaceContainerHighest, $tertiaryFixed);
}

.group-saadi-short {
    padding: 0 0.5rem;
    @include full-rounding;
    margin: 0.6rem 0.4rem;
    background-color: $layer1;
}

.bar-group-pad {
    padding: 0.205rem;
}

.bar-group-pad-less {
    padding: 0rem 0.681rem;
}

.bar-group-pad-system {
    padding: 0rem 0.341rem;
}

.bar-group-pad-vertical {
    @include bar-group-rounding;
    -gtk-outline-radius: 1.364rem;
    padding: 1rem 0rem;
    margin: 0rem 0.5rem;
}

.bar-group-pad-music {
    padding-right: 1.023rem;
    padding-left: 0.341rem;
}

.bar-group-standalone {
    @include bar-group-rounding;
    -gtk-outline-radius: 1.364rem;
}

.bar-group-round {
    border-radius: 10rem;
    -gtk-outline-radius: 10rem;
}

.bar-group-middle {
    border-radius: 0.477rem;
    -gtk-outline-radius: 0.477rem;
}

.bar-group-left {
    border-radius: 0.477rem;
    -gtk-outline-radius: 0.477rem;
    border-top-left-radius: 1.364rem;
    border-bottom-left-radius: 1.364rem;
}

.bar-group-right {
    border-radius: 0.477rem;
    -gtk-outline-radius: 0.477rem;
    border-top-right-radius: 1.364rem;
    border-bottom-right-radius: 1.364rem;
}

.bar-sidemodule {
    min-width: 26rem;
}

.bar-ws-width {
    min-width: 18.341rem;
}

.bar-ws-container {
    transition: 700ms cubic-bezier(0.1, 1, 0, 1);
}

.active-window-tb {
    background-color: $primary;
    @include large-rounding;
}

.bar-ws {
    font-size: 1.02rem;
    font-weight: 600;
    min-width: $bar_ws_width;
    color: $workspaceOnLayer1Inactive;
}

.bar-ws-active {
    background-color: $workspaceLayer3;
    color: $workspaceOnLayer3;
}

.bar-ws-occupied {
    background-color: $layer2;
    color: $workspaceOnLayer2;
}

// Focus is the bar mode name, not the workspace state!

.bar-ws-focus {
    background-color: $surfaceVariant;
    min-width: $bar_ws_width_focus;
}

.bar-ws-focus-active {
    min-width: $bar_ws_width_focus_active;
    background-color: $primaryFixed;
}

.bar-ws-focus-occupied {
    background-color: $secondaryContainer;
}

.bar-clock-box {
    margin: 0rem 0.682rem;
}

.bar-time {
    @include titlefont;
    font-size: 1.2727rem;
    color: $timeOnLayer1;
}

.power-draw-text {
    @include titlefont;
    font-size: 1.2rem;
    color: $timeOnLayer1;
}

.bar-date {
    color: $dateOnLayer1;
}

.bar-bat {
    padding: 0 3px;
    @include full-rounding;
    @include element_decel;
    min-height: 2.3rem;
    min-width: 2.3rem;
    border-radius: 10rem;
    color: $battOnLayer2;
}

.bar-bat-circprog {
    border: 0.12rem solid $outline;
    @include fluent_decel_long;
    min-width: 0.23rem; // line width
    min-height: 2.1rem;
    padding: 0rem;
    background-color: $battLayer2;
    color: $battOnLayer2;
}


.bar-music-art {
    margin: 4px;
    border: 1px solid $surfaceContainerHighest;
    background-color: $surfaceContainerHighest;
    border-radius: 8px;
    padding: 4px;
    -gtk-icon-style: regular;
    -gtk-icon-shadow: none;
}


.bar-batt {
    @include full-rounding;
    @include element_decel;
    min-height: 1.77rem;
    min-width: 1.77rem;
    border-radius: 10rem;
    color: $battOnLayer2;
}

.bar-batt-txt {
    color: $battOnLayer1;
}

.bar-batt-circprog {
    @include fluent_decel_long;
    min-width: 0.136rem; // line width
    min-height: 1.636rem;
    padding: 0rem;
    background-color: $battLayer2;
    color: $battOnLayer2;
}

.bar-batt-circprog-low {
    background-color: $error;
    color: $errorContainer;
}

.bar-batt-low {
    background-color: $error;
    color: $errorContainer;
}

.bar-ram-icon {
    @include full-rounding;
    @include element_decel;
    min-height: 1.77rem;
    min-width: 1.77rem;
    border-radius: 10rem;
    color: $ramOnLayer2;
}

.bar-ram-circprog {
    @include fluent_decel_long;
    min-width: 0.136rem; // line width
    min-height: 1.636rem;
    padding: 0rem;
    background-color: $ramLayer2;
    color: $ramOnLayer2;
}

.bar-ram-txt {
    color: $ramOnLayer1;
}

.bar-swap-icon {
    @include full-rounding;
    @include element_decel;
    min-height: 1.77rem;
    min-width: 1.77rem;
    border-radius: 10rem;
    color: $swapOnLayer2;
}

.bar-swap-circprog {
    @include fluent_decel_long;
    min-width: 0.136rem; // line width
    min-height: 1.636rem;
    padding: 0rem;
    background-color: $swapLayer2;
    color: $swapOnLayer2;
}

.bar-swap-txt {
    color: $swapOnLayer1;
}

.bar-cpu-icon {
    @include full-rounding;
    @include element_decel;
    min-height: 1.77rem;
    min-width: 1.77rem;
    border-radius: 10rem;
    color: $cpuOnLayer2;
}

.bar-cpu-circprog {
    @include fluent_decel_long;
    min-width: 0.136rem; // line width
    min-height: 1.636rem;
    padding: 0rem;
    background-color: $cpuLayer2;
    color: $cpuOnLayer2;
}

.bar-cpu-txt {
    color: $cpuOnLayer1;
}

.bar-music-playstate {
    @include element_decel;
    min-height: 1.77rem;
    min-width: 1.77rem;
    border-radius: 10rem;
    color: $musicOnLayer2;
}

.bar-music-circprog {
    @include fluent_decel_long;
    min-width: 0.136rem; // line width
    min-height: 1.636rem;
    padding: 0rem;
    background-color: $musicLayer2;
    color: $musicOnLayer2;
}

.bar-music-playstate-playing {
    min-height: 1.77rem;
    min-width: 1.77rem;
    border-radius: 10rem;
    color: $musicOnLayer2;
}

.bar-music-playstate-txt {
    transition: 100ms cubic-bezier(0.05, 0.7, 0.1, 1);
    @include icon-material;
}

.bar-music-txt {
    color: $musicOnLayer1;
}

.bar-music-cover {
    background-position: center;
    background-repeat: no-repeat;
    background-size: 100% auto;
    min-width: 11.932rem;
}

.bar-music-extended-bg {
    border-radius: 1.364rem;
    min-width: 34.091rem;
}

.bar-music-hide-false {
    @include menu_decel;
    transition-duration: 100ms;
    opacity: 1;
}

.bar-music-hide-true {
    @include menu_accel;
    transition-duration: 100ms;
    opacity: 0;
}

.bar-corner-spacing {
    min-width: 0.882rem;
    min-height: $rounding_large;
}

.corner {
    background-color: $layer0;
    border-radius: $rounding_large;
}

.corner-black {
    background-color: $black;
    padding: 100px
}

.bar-wintitle-topdesc {
    margin-top: -0.136rem;
    margin-bottom: -0.341rem;
    color: $windowtitleOnLayer0Inactive;
}

.bar-wintitle-txt {
    color: $windowtitleOnLayer0;
}

.bar-space-button {
    padding: 0.341rem;
}

.bar-space-button>box:first-child {
    @include full-rounding;
    padding: 0rem 0.682rem;
}

.bar-space-button-leftmost {
    box {
        margin: 0rem 0.682rem;
    }
}

.bar-space-area-rightmost>box {
    padding-right: 1.364rem;
}

.bar-systray {
    @include full-rounding;
    margin: 0.137rem 0rem;
    padding: 0rem 0.682rem;
}

.bar-systray-item {
    @include full-rounding;
    @include element_decel;
    @include symbolic-icon;
    min-height: 1.032rem;
    min-width: 1.032rem;
    font-size: 1.032rem;
    color: $trayOnLayer0;
}

.bar-statusicons {
    @include full-rounding;
    @include element_decel;
    margin: 0.273rem;
    padding: 0rem 0.614rem;
}

.bar-statusicons-active {
    background-color: $layer0Active;
    color: $onLayer0Active;
}

.bar-util-btn {
    @include full-rounding;
    @include element_decel;
    min-height: 1.77rem;
    min-width: 1.77rem;
    background-color: $utilsLayer2;
    color: $utilsOnLayer2;
}

.bar-util-btn2 {
    @include full-rounding;
    @include element_decel;
    min-height: 1.77rem;
    min-width: 1.77rem;
    background-color: $layer0;
    color: $utilsOnLayer2;
}

.bar-util-btn:hover,
.bar-util-btn:focus {
    background-color: $layer2Hover;
}

.bar-util-btn:active {
    background-color: $layer2Active;
}

.bar-spaceright {
    color: $barspacerightOnLayer0;
}

.bar-bluetooth-device {
    @include full-rounding;
    @include symbolic-icon;
    min-height: 1.032rem;
    min-width: 1.032rem;
    font-size: 1.032rem;
    padding: 0.205rem 0.341rem;
}

.bar-time-module {
    margin-left: 0.5rem;
    margin-right: 1rem;
    color: $timeOnLayer1;
}

.status-icons-group {
    padding-left: 0.273rem;
    padding-right: 1rem;
}

.time-with-margin {
    margin-left: 0.5rem;
}

.bar-wintitle-icon-spacer {
    min-width: 0.714rem;
}

.battery-scale-container {
    @include element_decel;
    @include small-rounding;
    padding: 0 6px;
    background: $layer1;
}

.battery-scale-box {
    min-width: 100px;
}

.battery-icon-box {
    padding: 0 2px;

    &.bar-batt-charging {
        color: $primary;
    }

    &.bar-batt-low {
        color: $error;
    }
}

.battery-scale-bar {
    min-width: 50px;
    background-color: $onSurfaceVariant;
    border-radius: 99px;

    trough progress {
        // background-color: $primary;
        background: linear-gradient(90deg, $secondary 1%, $primaryContainer);
        animation: chargeAnimation 10s linear infinite;
        border-radius: 99px;
        min-height: 15.5px;
    }

    &.bar-batt-charging trough progress {
        background: linear-gradient(90deg, $black, $primary);
        border-radius: 99px;
        min-height: 15.5px;
        animation: chargeAnimation 2s linear infinite;
    }

    &.bar-batt-low trough progress {
        background-color: $error;
        border-radius: 99px;
    }

}

@keyframes chargeAnimation {
    0% {
        background-position: 0% 50%;
    }

    100% {
        background-position: 100% 50%;
    }
}

.avatar-widget {
    @include normal-rounding;
    padding: 3px 6px;
    margin: 3px;
    background-color: $layer2;
}

.avatar-box {
    @include element_decel;
    box-shadow: inset 0 0 0 1px $layer1;
}

.avatar-eventbox:hover {
    .avatar-widget {
        background-color: $hovercolor;
    }
}

.avatar-eventbox:active {
    .avatar-widget {
        background-color: $activecolor;
    }
}

// // Module switcher styles
// .module-switcher {
//     min-width: 2.5rem;
//     min-height: 2.5rem;

//     stack {
//         min-height: 2.5rem;
//         padding: 0;
//         margin: 0;
//     }

//     .module-box {
//         min-height: 2.5rem;
//         padding: 0.25rem;
//         margin: 0;
//         border-radius: $rounding-small;
//         background-color: $layer2;
//         transition: $transition;

//         &:hover {
//             background-color: $hovercolor;
//         }
//     }
// }