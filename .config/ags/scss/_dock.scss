.dock-bg {
    @include shadows1;
    background-color: $layer0;
    margin-top: 1rem;
    border-top : 0.5px solid $outliner; 
}
.dock-round {
    border-top : 0.5px solid $outliner; 
    border-radius: $rounding_medium $rounding_medium 0 0;
}
.dock-app-btn-animate {
    transition-property: color;
    transition-duration: 0.5s;
}

.dock-app-btn {
    @include normal-rounding;
}
.unpinned-dock-app-btn {
    color: $surfaceContainerLowest;

}
.pinned-dock-app-btn {
    @include full-rounding;
    color: $secondary;
}

.dock-app-btn:hover,
.dock-app-btn:focus {
    background-color: $layer0Hover;
}

.dock-app-btn:active {
    background-color: $layer0Active;
}

.dock-app-icon {
    min-width: 3.409rem;
    min-height: 3.409rem;
    font-size: 3.409rem;
}

.dock-separator {
    min-width: 0.04rem;
    opacity: 0.4;
    margin:0.45rem 0.6rem;
    background-color: $outline;
}
