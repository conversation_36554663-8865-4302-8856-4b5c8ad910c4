windowrule = opacity 1 override, class:.*
# windowrule = noblur,.*
windowrule = float, title:^(blueberry.py)$
windowrule = float, title:^(steam)$
windowrule = unset,title:^(foot)$
windowrule = float, title:^(guifetch)$ # FlafyDev/guifetch
windowrule = tile, class:(dev.warp.Warp)
windowrule = float, title:^([Pp]icture[-\s]?[Ii]n[-\s]?[Pp]icture)(.*)$
windowrule = center, title:^(Open File)(.*)$
windowrule = center, title:^(Select a File)(.*)$
windowrule = center, title:^(Choose wallpaper)(.*)$
windowrule = center, title:^(Open Folder)(.*)$
windowrule = center, title:^(Save As)(.*)$
windowrule = center, title:^(Library)(.*)$
windowrule = center, title:^(File Upload)(.*)$
# Picture-in-Picture
windowrule = keepaspectratio, title:^(Picture(-| )in(-| )[Pp]icture)$
windowrule = move 73% 72%,title:^(Picture(-| )in(-| )[Pp]icture)$
windowrule = size 255%, title:^(Picture(-| )in(-| )[Pp]icture)$
windowrule = float, title:^(Picture(-| )in(-| )[Pp]icture)$
windowrule = pin, title:^(Picture(-| )in(-| )[Pp]icture)$

# Dialogs
windowrule=float,title:^(Open File)(.*)$
windowrule=float,title:^(Open Files)(.*)$
windowrule=float,title:^(Select a File)(.*)$
windowrule=float,title:^(Choose wallpaper)(.*)$
windowrule=float,title:^(Open Folder)(.*)$
windowrule=float,title:^(Save As)(.*)$
windowrule=float,title:^(Library)(.*)$
windowrule=float,title:^(File Upload)(.*)$

# Tearing
windowrule=immediate, class:.*\.exe
windowrule=immediate,class:(steam_app)

# ######## Layer rules ########
layerrule = xray 1, .*
layerrule = animation slide left, sideleft.*
layerrule = animation slide left, rofi.*
layerrule = animation slide right, sideright.*
layerrule = animation slide bottom, music.*
layerrule = animation slide top, wallselect.*
layerrule = animation fade ,cheatsheet.*
layerrule = animation fade ,desktopbackground.*
layerrule = animation slide top, colorscheme.*
layerrule = animation slide top, glance.*
layerrule = animation slide right, recorder.*
layerrule = animation slide bottom, overview.*
layerrule = animation fade, session.*
layerrule = noanim, hyprpicker.*
layerrule = blur, gtk-layer-shell
layerrule = ignorezero, gtk-layer-shell
layerrule = blur, launcher
layerrule = ignorezero, launcher
layerrule = blur, notifications
layerrule = ignorezero, notifications

# ags
layerrule = blur, session.*
layerrule = blur, rofi.*
layerrule = ignorealpha 0.55, rofi.*
layerrule = blur, bar.*
layerrule = ignorealpha 0.55, bar.*
layerrule = blur, wallselect.*
layerrule = ignorealpha 0.55, wallselect.*
layerrule = blur, corner.*
layerrule = ignorealpha 0.55, corner.*
layerrule = blur, dock.*
layerrule = ignorealpha 0.55, dock.*
layerrule = ignorealpha 0.55, indicator.*
layerrule = blur, overview
layerrule = ignorealpha 0.55, overview
layerrule = blur, cheatsheet.*
layerrule = ignorealpha 0.55, cheatsheet.*
layerrule = blur, sideright.*
layerrule = ignorealpha 0.55, sideright.*
layerrule = blur, sideleft.*
layerrule = ignorealpha 0.55, sideleft.*
layerrule = blur, indicator.*
layerrule = ignorealpha 0.55, indicator.*
layerrule = blur, music.*
layerrule = ignorealpha 0.55, music.*
layerrule = blur, colorscheme.*
layerrule = ignorealpha 0.55, colorscheme.*
layerrule = blur, recorder.*
layerrule = ignorealpha 0.55, recorder.*
layerrule = blur, glance.*
layerrule = ignorealpha 0.55, glance.*

